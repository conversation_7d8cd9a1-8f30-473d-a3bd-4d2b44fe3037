<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Portal - Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        /* Epic Animated Background */
        .login-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        .login-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 128, 0.1) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        .login-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 212, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.02) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 30s linear infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(1deg); }
            66% { transform: translateY(-20px) rotate(-1deg); }
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Login Container */
        .login-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
            padding: 2rem;
        }

        /* Epic Login Card */
        .epic-login-card {
            background: rgba(10, 10, 15, 0.9);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            animation: cardFloat 6s ease-in-out infinite;
        }

        @keyframes cardFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .epic-login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.1) 0%,
                rgba(255, 0, 128, 0.1) 50%,
                rgba(0, 212, 255, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .epic-login-card:hover::before {
            opacity: 1;
        }

        /* Login Header */
        .login-header {
            padding: 3rem 2.5rem 2rem;
            text-align: center;
            position: relative;
        }

        .admin-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            position: relative;
            transition: all 0.3s ease;
        }

        .admin-icon::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 23px;
            opacity: 0;
            filter: blur(10px);
            transition: opacity 0.3s ease;
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { opacity: 0; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.1); }
        }

        .admin-icon i {
            font-size: 2.5rem;
            color: white;
            z-index: 2;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
            letter-spacing: 1px;
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.6);
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        /* Login Body */
        .login-body {
            padding: 0 2.5rem 3rem;
        }

        /* Epic Alert */
        .epic-alert {
            background: rgba(220, 53, 69, 0.1) !important;
            border: 1px solid rgba(220, 53, 69, 0.3) !important;
            border-radius: 15px !important;
            color: #ff6b6b !important;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            animation: alertSlide 0.3s ease-out;
        }

        @keyframes alertSlide {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .epic-alert i {
            font-size: 1.2rem;
            color: #ff6b6b;
        }

        /* Epic Form */
        .epic-form {
            position: relative;
        }

        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .remember-me-group {
            margin-bottom: 1.5rem;
        }

        .epic-checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .epic-checkbox {
            display: none;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .checkbox-label:hover {
            color: #00d4ff;
        }

        .checkbox-custom {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.05);
            position: relative;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .checkbox-custom::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 6px;
            width: 6px;
            height: 10px;
            border: solid #00d4ff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg) scale(0);
            transition: all 0.3s ease;
        }

        .epic-checkbox:checked + .checkbox-label .checkbox-custom {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }

        .epic-checkbox:checked + .checkbox-label .checkbox-custom::after {
            transform: rotate(45deg) scale(1);
        }

        .checkbox-text {
            user-select: none;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
            margin-bottom: 0.75rem;
            font-size: 0.95rem;
            letter-spacing: 0.5px;
        }

        .epic-input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            z-index: 3;
            color: rgba(255, 255, 255, 0.5);
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .epic-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.05) !important;
            border: 2px solid rgba(255, 255, 255, 0.1) !important;
            border-radius: 15px !important;
            padding: 1rem 1rem 1rem 3rem !important;
            color: white !important;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .epic-input::placeholder {
            color: rgba(255, 255, 255, 0.4) !important;
        }

        .epic-input:focus {
            background: rgba(0, 212, 255, 0.1) !important;
            border-color: #00d4ff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25) !important;
            outline: none !important;
        }

        .epic-input:focus + .input-icon {
            color: #00d4ff;
            transform: scale(1.1);
        }

        /* Epic Login Button */
        .epic-login-btn {
            width: 100%;
            background: linear-gradient(135deg, #00d4ff, #0099cc) !important;
            border: none !important;
            border-radius: 15px !important;
            padding: 1rem 2rem !important;
            color: white !important;
            font-weight: 700 !important;
            font-size: 1.1rem !important;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
            margin-bottom: 2rem;
        }

        .epic-login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .epic-login-btn:hover {
            color: white !important;
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 212, 255, 0.4);
        }

        .epic-login-btn:hover::before {
            left: 100%;
        }

        .epic-login-btn:active {
            transform: translateY(-1px);
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            position: relative;
            z-index: 2;
        }

        .btn-content i {
            font-size: 1.2rem;
        }

        /* Back Link */
        .back-link {
            text-align: center;
        }

        .back-link a {
            color: rgba(255, 255, 255, 0.6) !important;
            text-decoration: none !important;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .back-link a:hover {
            color: #00d4ff !important;
            background: rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        .back-link i {
            font-size: 0.9rem;
        }

        /* Security Badge */
        .security-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: #00d4ff;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: badgePulse 2s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-container {
                padding: 1rem;
                max-width: 100%;
            }

            .login-header {
                padding: 2rem 1.5rem 1.5rem;
            }

            .login-body {
                padding: 0 1.5rem 2rem;
            }

            .login-title {
                font-size: 1.75rem;
            }

            .security-badge {
                position: static;
                margin-bottom: 1rem;
                align-self: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-bg"></div>

    <div class="login-container">
        <div class="epic-login-card">
            <div class="security-badge">
                <i class="bi bi-shield-check"></i>
                <span>Secure Login</span>
            </div>

            <div class="login-header">
                <div class="admin-icon">
                    <i class="bi bi-shield-lock"></i>
                </div>
                <h1 class="login-title">Admin Portal</h1>
                <p class="login-subtitle">Gaming Accounts Store</p>
            </div>

            <div class="login-body">
                <% if (error) { %>
                <div class="alert epic-alert" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span><%= error %></span>
                </div>
                <% } %>

                <form action="/admin/login" method="POST" class="epic-form">
                    <div class="form-group">
                        <label for="username" class="form-label">Username</label>
                        <div class="epic-input-group">
                            <input type="text" class="form-control epic-input" id="username" name="username"
                                   placeholder="Enter your username" required autocomplete="username">
                            <i class="bi bi-person input-icon"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="epic-input-group">
                            <input type="password" class="form-control epic-input" id="password" name="password"
                                   placeholder="Enter your password" required autocomplete="current-password">
                            <i class="bi bi-lock input-icon"></i>
                        </div>
                    </div>

                    <div class="form-group remember-me-group">
                        <div class="epic-checkbox-group">
                            <input type="checkbox" class="epic-checkbox" id="rememberMe" name="rememberMe">
                            <label for="rememberMe" class="checkbox-label">
                                <span class="checkbox-custom"></span>
                                <span class="checkbox-text">Remember me for 30 days</span>
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn epic-login-btn">
                        <div class="btn-content">
                            <i class="bi bi-box-arrow-in-right"></i>
                            <span>Access Admin Panel</span>
                        </div>
                    </button>
                </form>

                <div class="back-link">
                    <a href="/">
                        <i class="bi bi-arrow-left"></i>
                        <span>Back to Home</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Focus effect for inputs
            const inputs = document.querySelectorAll('.epic-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // Login button click effect
            const loginBtn = document.querySelector('.epic-login-btn');
            loginBtn.addEventListener('click', function(e) {
                // Create ripple effect
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.left = '50%';
                ripple.style.top = '50%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.marginLeft = '-10px';
                ripple.style.marginTop = '-10px';

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
