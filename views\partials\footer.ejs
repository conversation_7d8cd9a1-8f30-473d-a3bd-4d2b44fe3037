<!-- Epic Gaming Footer -->
<footer class="epic-footer">
    <!-- Animated Background -->
    <div class="footer-bg">
        <div class="bg-particles"></div>
        <div class="bg-grid"></div>
    </div>

    <!-- Main Footer Content -->
    <div class="footer-main">
        <div class="container">
            <div class="row g-5">
                <!-- Brand & CTA Section -->
                <div class="col-lg-5">
                    <div class="footer-brand-section">
                        <!-- Logo -->
                        <div class="footer-logo">
                            <div class="logo-icon">
                                <i class="bi bi-controller"></i>
                                <div class="logo-pulse"></div>
                            </div>
                            <div class="logo-text">
                                <h3>GAMING STORE</h3>
                                <span>Premium Accounts Marketplace</span>
                            </div>
                        </div>

                        <!-- Description -->
                        <p class="footer-description">
                            The ultimate destination for premium gaming accounts. Secure transactions,
                            instant delivery, and verified accounts from trusted sellers worldwide.
                        </p>

                        <!-- CTA Section -->
                        <div class="footer-cta">
                            <h5>Ready to Level Up?</h5>
                            <a href="https://t.me/<%= typeof botUsername !== 'undefined' ? botUsername : 'YourBot' %>" class="footer-cta-btn">
                                <i class="bi bi-telegram"></i>
                                <span>Start Shopping Now</span>
                                <div class="btn-shine"></div>
                            </a>
                        </div>

                        <!-- Stats -->
                        <div class="footer-stats">
                            <div class="stat-item">
                                <div class="stat-number">1000+</div>
                                <div class="stat-label">Accounts Sold</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">Support</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">100%</div>
                                <div class="stat-label">Secure</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="col-lg-2 col-md-4">
                    <div class="footer-section">
                        <h6 class="footer-title">
                            <i class="bi bi-list-ul"></i>
                            Navigation
                        </h6>
                        <ul class="footer-links">
                            <li><a href="/"><i class="bi bi-house"></i>Home</a></li>
                            <li><a href="#features"><i class="bi bi-star"></i>Features</a></li>
                            <li><a href="/health"><i class="bi bi-heart-pulse"></i>System Status</a></li>
                            <li><a href="/admin"><i class="bi bi-shield-lock"></i>Admin Panel</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Gaming Categories -->
                <div class="col-lg-2 col-md-4">
                    <div class="footer-section">
                        <h6 class="footer-title">
                            <i class="bi bi-controller"></i>
                            Categories
                        </h6>
                        <ul class="footer-links">
                            <li><a href="#"><i class="bi bi-phone"></i>Mobile Games</a></li>
                            <li><a href="#"><i class="bi bi-pc-display"></i>PC Games</a></li>
                            <li><a href="#"><i class="bi bi-nintendo-switch"></i>Console Games</a></li>
                            <li><a href="#"><i class="bi bi-trophy"></i>Premium Accounts</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Support & Security -->
                <div class="col-lg-3 col-md-4">
                    <div class="footer-section">
                        <h6 class="footer-title">
                            <i class="bi bi-shield-check"></i>
                            Security & Support
                        </h6>

                        <!-- Security Features -->
                        <div class="security-features">
                            <div class="security-item">
                                <i class="bi bi-lock-fill"></i>
                                <div>
                                    <strong>Secure Payments</strong>
                                    <span>KHQR Protected Transactions</span>
                                </div>
                            </div>
                            <div class="security-item">
                                <i class="bi bi-lightning-charge-fill"></i>
                                <div>
                                    <strong>Instant Delivery</strong>
                                    <span>Automated Account Transfer</span>
                                </div>
                            </div>
                            <div class="security-item">
                                <i class="bi bi-headset"></i>
                                <div>
                                    <strong>24/7 Support</strong>
                                    <span>Always Here to Help</span>
                                </div>
                            </div>
                        </div>

                        <!-- Trust Badges -->
                        <div class="trust-badges">
                            <div class="trust-badge">
                                <i class="bi bi-patch-check-fill"></i>
                                <span>Verified</span>
                            </div>
                            <div class="trust-badge">
                                <i class="bi bi-shield-fill-check"></i>
                                <span>Secure</span>
                            </div>
                            <div class="trust-badge">
                                <i class="bi bi-award-fill"></i>
                                <span>Premium</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="copyright">
                        <i class="bi bi-c-circle"></i>
                        <span>2025 Gaming Accounts Store. All rights reserved.</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="footer-tech">
                        <span>Powered by</span>
                        <div class="tech-stack">
                            <span class="tech-item">
                                <i class="bi bi-code-slash"></i>Node.js
                            </span>
                            <span class="tech-item">
                                <i class="bi bi-telegram"></i>Telegram API
                            </span>
                            <span class="tech-item">
                                <i class="bi bi-database"></i>SQLite
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
/* Epic Footer Styles */
.epic-footer {
    position: relative;
    background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
    margin-top: 5rem;
    overflow: hidden;
}

/* Animated Background */
.footer-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.bg-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(0, 255, 128, 0.05) 0%, transparent 50%);
    animation: particleFloat 20s ease-in-out infinite;
}

.bg-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s linear infinite;
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Main Footer Content */
.footer-main {
    position: relative;
    z-index: 2;
    padding: 5rem 0 3rem;
}

/* Brand Section */
.footer-brand-section {
    position: relative;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.logo-icon {
    position: relative;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #00d4ff, #ff0080);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.logo-icon i {
    font-size: 2.5rem;
    color: white;
    z-index: 2;
}

.logo-pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, #00d4ff, #ff0080);
    border-radius: 25px;
    opacity: 0;
    animation: logoPulse 3s ease-in-out infinite;
}

@keyframes logoPulse {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: 0.3; transform: scale(1.1); }
}

.logo-text h3 {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: 1px;
}

.logo-text span {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
}

.footer-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.7;
    margin-bottom: 2.5rem;
    font-size: 1.1rem;
}

/* CTA Section */
.footer-cta {
    margin-bottom: 3rem;
}

.footer-cta h5 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.footer-cta-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #00d4ff, #0099cc);
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.footer-cta-btn:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.4);
}

.footer-cta-btn:hover .btn-shine {
    left: 100%;
}

/* Stats */
.footer-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #00d4ff, #ff0080);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.stat-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Footer Sections */
.footer-section {
    height: 100%;
}

.footer-title {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #00d4ff, #ff0080);
    border-radius: 2px;
}

.footer-title i {
    color: #00d4ff;
    font-size: 1.1rem;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 1rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    border-radius: 8px;
    position: relative;
}

.footer-links a:hover {
    color: #00d4ff;
    padding-left: 1rem;
    background: rgba(0, 212, 255, 0.1);
}

.footer-links a i {
    width: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

/* Security Features */
.security-features {
    margin-bottom: 2rem;
}

.security-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(0, 212, 255, 0.1);
    transition: all 0.3s ease;
}

.security-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
    transform: translateX(5px);
}

.security-item i {
    color: #00d4ff;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.security-item strong {
    color: #ffffff;
    display: block;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.security-item span {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Trust Badges */
.trust-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #00d4ff;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.trust-badge:hover {
    background: rgba(0, 212, 255, 0.2);
    transform: translateY(-2px);
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    position: relative;
    z-index: 2;
}

.copyright {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.copyright i {
    color: #00d4ff;
}

.footer-tech {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: flex-end;
    color: rgba(255, 255, 255, 0.6);
}

.tech-stack {
    display: flex;
    gap: 1rem;
}

.tech-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.tech-item:hover {
    color: #00d4ff;
}

.tech-item i {
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer-main {
        padding: 3rem 0 2rem;
    }

    .footer-logo {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-stats {
        justify-content: center;
    }

    .footer-tech {
        justify-content: center;
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .tech-stack {
        justify-content: center;
    }

    .trust-badges {
        justify-content: center;
    }
}
.modern-footer {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.95), rgba(26, 26, 46, 0.95));
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 5rem;
    position: relative;
    overflow: hidden;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.footer-content {
    padding: 4rem 0 2rem;
    position: relative;
    z-index: 2;
}

.footer-brand .brand-logo {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-brand .brand-logo i {
    font-size: 2rem;
    background: linear-gradient(135deg, #00d4ff, #ff0080);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: 0.75rem;
}

.footer-brand .brand-logo span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
}

.brand-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: linear-gradient(135deg, #00d4ff, #ff0080);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.footer-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(135deg, #00d4ff, #ff0080);
    border-radius: 1px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    position: relative;
    padding-left: 0;
}

.footer-links a:hover {
    color: #00d4ff;
    padding-left: 10px;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 1px;
    background: #00d4ff;
    transition: width 0.3s ease;
}

.footer-links a:hover::before {
    width: 6px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
}

.contact-item i {
    width: 20px;
    margin-right: 0.75rem;
    color: #00d4ff;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
    position: relative;
    z-index: 2;
}

.copyright,
.powered-by {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    font-size: 0.9rem;
}

.powered-by i {
    color: #00d4ff;
}

@media (max-width: 768px) {
    .footer-content {
        padding: 3rem 0 1.5rem;
    }

    .social-links {
        justify-content: center;
        margin-top: 1rem;
    }

    .footer-bottom .col-md-6:last-child {
        text-align: center !important;
        margin-top: 1rem;
    }
}
</style>
