<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title || 'Error' %> - Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 600px;
        }
        .error-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .error-body {
            padding: 2rem;
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .error-code {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-weight: bold;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .stack-trace {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="error-card">
                    <div class="error-header">
                        <div class="error-icon">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="error-code"><%= statusCode || 500 %></div>
                        <h3 class="mb-0">
                            <% if (statusCode === 404) { %>
                                Page Not Found
                            <% } else if (statusCode === 403) { %>
                                Access Forbidden
                            <% } else if (statusCode === 401) { %>
                                Unauthorized
                            <% } else if (statusCode === 429) { %>
                                Too Many Requests
                            <% } else { %>
                                Server Error
                            <% } %>
                        </h3>
                    </div>
                    
                    <div class="error-body">
                        <div class="text-center mb-4">
                            <p class="lead">
                                <% if (statusCode === 404) { %>
                                    The page you're looking for doesn't exist.
                                <% } else if (statusCode === 403) { %>
                                    You don't have permission to access this resource.
                                <% } else if (statusCode === 401) { %>
                                    Please log in to access this page.
                                <% } else if (statusCode === 429) { %>
                                    You've made too many requests. Please try again later.
                                <% } else { %>
                                    Something went wrong on our end.
                                <% } %>
                            </p>
                            
                            <% if (error && error.message) { %>
                            <div class="alert alert-danger" role="alert">
                                <%= error.message %>
                            </div>
                            <% } %>
                        </div>
                        
                        <div class="text-center mb-4">
                            <a href="/" class="btn-home me-2">
                                <i class="bi bi-house"></i>
                                Go Home
                            </a>
                            <button onclick="history.back()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i>
                                Go Back
                            </button>
                        </div>
                        
                        <% if (error && error.stack && process.env.NODE_ENV === 'development') { %>
                        <div class="mt-4">
                            <h6>Stack Trace (Development Mode)</h6>
                            <div class="stack-trace"><%= error.stack %></div>
                        </div>
                        <% } %>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                If this problem persists, please contact our support team.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
