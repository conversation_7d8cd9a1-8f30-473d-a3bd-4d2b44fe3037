<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gaming Accounts Store - Premium Gaming Accounts</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(1deg); }
            66% { transform: translateY(-20px) rotate(-1deg); }
        }

        /* Premium Header Styles */
        .premium-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(5, 5, 15, 0.95);
            backdrop-filter: blur(25px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
        }

        .premium-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                rgba(0, 212, 255, 0.05) 0%,
                rgba(255, 0, 128, 0.05) 50%,
                rgba(0, 212, 255, 0.05) 100%);
            pointer-events: none;
        }

        .premium-header .navbar {
            padding: 1rem 0;
            background: transparent !important;
        }
 s
        /* Premium Brand */
        .premium-brand {
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .brand-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .brand-icon {
            position: relative;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .brand-icon i {
            font-size: 1.8rem;
            color: white;
            z-index: 2;
        }

        .icon-glow {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 14px;
            opacity: 0;
            filter: blur(8px);
            transition: opacity 0.3s ease;
        }

        .premium-brand:hover .icon-glow {
            opacity: 0.6;
        }

        .premium-brand:hover .brand-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            line-height: 1;
        }

        .brand-name {
            font-size: 1.4rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 1px;
        }

        .brand-sub {
            font-size: 0.8rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            letter-spacing: 2px;
            margin-top: -2px;
        }

        /* Premium Navigation Links */
        .premium-nav-link {
            position: relative;
            color: rgba(255, 255, 255, 0.8) !important;
            font-weight: 500;
            padding: 0.75rem 1.5rem !important;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .premium-nav-link:hover {
            color: #00d4ff !important;
            background: rgba(0, 212, 255, 0.1);
        }

        .premium-nav-link.active {
            color: #00d4ff !important;
        }

        .link-underline {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #00d4ff, #ff0080);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .premium-nav-link:hover .link-underline,
        .premium-nav-link.active .link-underline {
            width: 80%;
        }

        /* Navbar Actions */
        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Premium Buttons */
        .premium-admin-btn {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .premium-admin-btn:hover {
            background: rgba(0, 212, 255, 0.2) !important;
            border-color: #00d4ff !important;
            transform: translateY(-1px);
        }

        .premium-login-btn {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .premium-login-btn:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            transform: translateY(-1px);
        }

        .premium-cta-btn {
            position: relative;
            background: linear-gradient(135deg, #00d4ff, #0099cc) !important;
            border: none !important;
            color: white !important;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            overflow: hidden;
        }

        .btn-glow {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 27px;
            opacity: 0;
            filter: blur(10px);
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .premium-cta-btn:hover {
            color: white !important;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
        }

        .premium-cta-btn:hover .btn-glow {
            opacity: 0.8;
        }

        /* Premium Dropdown */
        .premium-dropdown {
            background: rgba(5, 5, 15, 0.95) !important;
            backdrop-filter: blur(25px) !important;
            border: 1px solid rgba(0, 212, 255, 0.2) !important;
            border-radius: 12px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5) !important;
            padding: 0.5rem !important;
            min-width: 200px;
        }

        .premium-dropdown .dropdown-item {
            color: rgba(255, 255, 255, 0.8) !important;
            padding: 0.75rem 1rem !important;
            border-radius: 8px !important;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 2px 0 !important;
        }

        .premium-dropdown .dropdown-item:hover {
            background: rgba(0, 212, 255, 0.1) !important;
            color: #00d4ff !important;
            transform: translateX(5px);
        }

        .premium-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* Premium Mobile Toggle */
        .premium-toggler {
            background: none !important;
            border: none !important;
            padding: 0.5rem !important;
            width: 30px;
            height: 30px;
            position: relative;
            cursor: pointer;
        }

        .premium-toggler span {
            display: block;
            width: 20px;
            height: 2px;
            background: white;
            margin: 4px 0;
            transition: all 0.3s ease;
            border-radius: 1px;
        }

        .premium-toggler:not(.collapsed) span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .premium-toggler:not(.collapsed) span:nth-child(2) {
            opacity: 0;
        }

        .premium-toggler:not(.collapsed) span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding: 120px 0 80px;
        }

        .hero-content {
            text-align: center;
            z-index: 2;
        }

        .hero-title {
            font-size: clamp(2.5rem, 8vw, 4.5rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .cta-button {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 50px;
            padding: 18px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 212, 255, 0.4);
            color: white;
            background: linear-gradient(135deg, #0099cc, #00d4ff);
        }

        .cta-button i {
            font-size: 1.3rem;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Features Section */
        .features-section {
            padding: 100px 0;
            position: relative;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(255, 0, 128, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-color: rgba(0, 212, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 3.5rem;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #ffffff;
            position: relative;
            z-index: 2;
        }

        .feature-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        /* How it works section */
        .how-it-works {
            padding: 100px 0;
            background: rgba(255, 255, 255, 0.02);
        }

        .step-card {
            text-align: center;
            padding: 2rem 1rem;
        }

        .step-number {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 800;
            color: white;
            margin: 0 auto 1.5rem;
            position: relative;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .step-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .cta-button {
                padding: 15px 30px;
                font-size: 1rem;
            }

            .features-section,
            .how-it-works {
                padding: 60px 0;
            }
        }
    </style>
</head>
<body>
    <div class="bg-animation"></div>
    <%- include('partials/navbar') %>

    <div class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Premium Gaming Accounts
                </h1>
                <p class="hero-subtitle">
                    Discover verified gaming accounts with instant delivery. Secure payments, 24/7 support, and the best prices in the market.
                </p>
                <a href="https://t.me/<%= botUsername %>" class="cta-button">
                    <i class="bi bi-telegram"></i>
                    Start Shopping Now
                </a>
            </div>
        </div>
    </div>

    <div class="features-section">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <h4 class="feature-title">Secure Payments</h4>
                        <p class="feature-description">Advanced KHQR payment system with automatic verification. Your transactions are protected with bank-level security.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-lightning-charge"></i>
                        </div>
                        <h4 class="feature-title">Instant Delivery</h4>
                        <p class="feature-description">Get your gaming account credentials immediately after payment confirmation. No waiting, no delays, just instant access.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-headset"></i>
                        </div>
                        <h4 class="feature-title">24/7 Support</h4>
                        <p class="feature-description">Round-the-clock customer support through our intelligent Telegram bot. Get help whenever you need it.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="how-it-works">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="hero-title" style="font-size: 3rem; margin-bottom: 1rem;">How It Works</h2>
                <p class="hero-subtitle" style="margin-bottom: 0;">Simple steps to get your premium gaming account</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h5 class="step-title">Start Chat</h5>
                        <p class="step-description">Message our Telegram bot to begin your gaming journey</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h5 class="step-title">Add Funds</h5>
                        <p class="step-description">Top up your balance using secure KHQR payment system</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h5 class="step-title">Browse & Buy</h5>
                        <p class="step-description">Choose from our verified gaming accounts collection</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <h5 class="step-title">Get Account</h5>
                        <p class="step-description">Receive your account details instantly via Telegram</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%- include('partials/footer') %>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
