const axios = require('axios');
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const { log, formatCurrency, generateRandomString } = require('../utils/helpers');

class KHQRPaymentController {
    constructor(bot) {
        this.bot = bot;
        this.baseUrl = 'https://api.kunchhunlichhean.org/khqr';
        this.bakongId = process.env.KHQR_BAKONG_ID || 'chhunlichhean_kun@wing';
        this.merchantName = process.env.KHQR_MERCHANT_NAME || 'CHHEANSMM';
        this.bearerToken = process.env.KHQR_BEARER_TOKEN;
        this.paymentCheckers = new Map(); // Store active payment checkers
        this.cleanupInterval = null;

        this.startCleanupService();
        log('info', 'KHQR Payment Controller initialized with bearer token');
    }

    // Generate QR code for payment
    async generateQRCode(amount, userId) {
        try {
            const transactionId = generateRandomString(12);

            // Use GET request with query parameters and bearer token
            const apiUrl = `${this.baseUrl}/create?amount=${amount}&bakongid=${this.bakongId}&merchantname=${this.merchantName}`;

            const headers = {};
            if (this.bearerToken) {
                headers['Authorization'] = `Bearer ${this.bearerToken}`;
            }

            const response = await axios.get(apiUrl, {
                timeout: 10000,
                headers: headers
            });

            if (response.data && response.data.qr && response.data.md5) {
                log('info', `QR code generated for user ${userId}, amount: ${amount}`);
                return {
                    success: true,
                    qrUrl: response.data.qr,
                    md5Hash: response.data.md5,
                    transactionId: response.data.transactionid || transactionId
                };
            } else {
                log('error', 'Invalid response from KHQR API', response.data);
                return {
                    success: false,
                    error: 'Invalid response from payment service'
                };
            }
        } catch (error) {
            log('error', 'Error generating QR code', error);
            return {
                success: false,
                error: 'Failed to generate payment QR code'
            };
        }
    }

    // Check payment status
    async checkPaymentStatus(md5Hash) {
        try {
            const headers = {};
            if (this.bearerToken) {
                headers['Authorization'] = `Bearer ${this.bearerToken}`;
            }

            const response = await axios.get(`${this.baseUrl}/check?md5=${md5Hash}`, {
                timeout: 5000,
                headers: headers
            });

            if (response.data && response.data.responseCode !== undefined) {
                return {
                    success: true,
                    paid: response.data.responseCode === 0,
                    responseCode: response.data.responseCode
                };
            } else {
                return {
                    success: false,
                    error: 'Invalid response from payment service'
                };
            }
        } catch (error) {
            log('error', 'Error checking payment status', error);
            return {
                success: false,
                error: 'Failed to check payment status'
            };
        }
    }

    // Process topup request
    async processTopup(chatId, userId, amount) {
        try {
            // Generate QR code
            const qrResult = await this.generateQRCode(amount, userId);
            
            if (!qrResult.success) {
                await this.bot.sendMessage(chatId, 
                    `❌ Failed to generate payment QR code: ${qrResult.error}\n\nPlease try again later.`
                );
                return;
            }

            // Send QR code to user
            const qrMessage = await this.bot.sendPhoto(chatId, qrResult.qrUrl, {
                caption: `💳 <b>Payment QR Code</b>\n\n` +
                        `Amount: <b>${formatCurrency(amount)}</b>\n` +
                        `Scan this QR code with your banking app to complete the payment.\n\n` +
                        `⏰ This QR code will expire in 3 minutes.\n` +
                        `💡 Payment will be processed automatically.`,
                parse_mode: 'HTML'
            });

            // Create transaction record
            const transaction = await Transaction.create({
                user_id: userId,
                type: 'topup',
                amount: amount,
                md5_hash: qrResult.md5Hash,
                message_id: qrMessage.message_id,
                qr_url: qrResult.qrUrl,
                transaction_id: qrResult.transactionId
            });

            // Start payment checker
            this.startPaymentChecker(transaction, chatId);

            log('info', `Topup initiated for user ${userId}, amount: ${amount}, transaction: ${transaction.id}`);
        } catch (error) {
            log('error', 'Error processing topup', error);
            await this.bot.sendMessage(chatId, 
                'Sorry, something went wrong while processing your topup request. Please try again later.'
            );
        }
    }

    // Start payment checker for a transaction
    startPaymentChecker(transaction, chatId) {
        const checkerId = `${transaction.id}_${Date.now()}`;
        const startTime = Date.now();
        const timeoutMs = 3 * 60 * 1000; // 3 minutes
        
        const checker = {
            transactionId: transaction.id,
            chatId: chatId,
            md5Hash: transaction.md5_hash,
            messageId: transaction.message_id,
            startTime: startTime,
            timeoutMs: timeoutMs,
            checkCount: 0
        };

        this.paymentCheckers.set(checkerId, checker);
        
        // Start checking
        this.checkPayment(checkerId);
        
        log('info', `Payment checker started for transaction ${transaction.id}`);
    }

    // Check payment with recursive timeout
    async checkPayment(checkerId) {
        const checker = this.paymentCheckers.get(checkerId);
        if (!checker) return;

        const elapsed = Date.now() - checker.startTime;
        
        // Check if timeout exceeded
        if (elapsed >= checker.timeoutMs) {
            await this.handlePaymentTimeout(checkerId);
            return;
        }

        try {
            checker.checkCount++;
            const paymentStatus = await this.checkPaymentStatus(checker.md5Hash);
            
            if (paymentStatus.success && paymentStatus.paid) {
                await this.handlePaymentSuccess(checkerId);
                return;
            }
            
            // Continue checking after 1 second
            setTimeout(() => {
                this.checkPayment(checkerId);
            }, 1000);
            
        } catch (error) {
            log('error', `Error in payment checker ${checkerId}`, error);
            
            // Continue checking on error, but with longer delay
            setTimeout(() => {
                this.checkPayment(checkerId);
            }, 2000);
        }
    }

    // Handle successful payment
    async handlePaymentSuccess(checkerId) {
        const checker = this.paymentCheckers.get(checkerId);
        if (!checker) return;

        try {
            // Get transaction
            const transaction = await Transaction.getById(checker.transactionId);
            if (!transaction || transaction.status !== 'pending') {
                log('warn', `Transaction ${checker.transactionId} not found or not pending`);
                this.paymentCheckers.delete(checkerId);
                return;
            }

            // Update user balance
            await User.updateBalance(transaction.user_id, transaction.amount, 'add');
            
            // Mark transaction as completed
            await transaction.markCompleted();
            
            // Delete QR message
            try {
                await this.bot.deleteMessage(checker.chatId, checker.messageId);
            } catch (deleteError) {
                log('warn', 'Failed to delete QR message', deleteError);
            }
            
            // Send success message
            const user = await User.getByTelegramId(transaction.user_id);
            await this.bot.sendMessage(checker.chatId, 
                `✅ <b>Payment Successful!</b>\n\n` +
                `Amount: <b>${formatCurrency(transaction.amount)}</b>\n` +
                `New Balance: <b>${formatCurrency(user.balance)}</b>\n\n` +
                `Thank you for your payment! 🎉`,
                { parse_mode: 'HTML' }
            );

            log('info', `Payment completed for transaction ${transaction.id}`);
        } catch (error) {
            log('error', `Error handling payment success for ${checkerId}`, error);
        } finally {
            this.paymentCheckers.delete(checkerId);
        }
    }

    // Handle payment timeout
    async handlePaymentTimeout(checkerId) {
        const checker = this.paymentCheckers.get(checkerId);
        if (!checker) return;

        try {
            // Mark transaction as expired
            const transaction = await Transaction.getById(checker.transactionId);
            if (transaction && transaction.status === 'pending') {
                await transaction.markExpired();
            }
            
            // Delete QR message
            try {
                await this.bot.deleteMessage(checker.chatId, checker.messageId);
            } catch (deleteError) {
                log('warn', 'Failed to delete expired QR message', deleteError);
            }
            
            // Send timeout message
            await this.bot.sendMessage(checker.chatId, 
                `⏰ <b>Payment Expired</b>\n\n` +
                `The payment QR code has expired. Please try again with /topup if you still want to add funds.`,
                { parse_mode: 'HTML' }
            );

            log('info', `Payment expired for transaction ${checker.transactionId}`);
        } catch (error) {
            log('error', `Error handling payment timeout for ${checkerId}`, error);
        } finally {
            this.paymentCheckers.delete(checkerId);
        }
    }

    // Start cleanup service for expired transactions
    startCleanupService() {
        this.cleanupInterval = setInterval(async () => {
            try {
                await this.cleanupExpiredTransactions();
            } catch (error) {
                log('error', 'Error in cleanup service', error);
            }
        }, 60000); // Run every minute

        log('info', 'Cleanup service started');
    }

    // Cleanup expired transactions
    async cleanupExpiredTransactions() {
        try {
            const expiredTransactions = await Transaction.getPendingTransactions(3);
            
            for (const transaction of expiredTransactions) {
                try {
                    // Mark as expired
                    await transaction.markExpired();
                    
                    // Try to delete QR message if message_id exists
                    if (transaction.message_id) {
                        try {
                            await this.bot.deleteMessage(transaction.user_id, transaction.message_id);
                        } catch (deleteError) {
                            // Message might already be deleted or chat not accessible
                            log('warn', `Failed to delete message ${transaction.message_id}`, deleteError);
                        }
                    }
                    
                    log('info', `Cleaned up expired transaction ${transaction.id}`);
                } catch (error) {
                    log('error', `Error cleaning up transaction ${transaction.id}`, error);
                }
            }
            
            if (expiredTransactions.length > 0) {
                log('info', `Cleaned up ${expiredTransactions.length} expired transactions`);
            }
        } catch (error) {
            log('error', 'Error in cleanup expired transactions', error);
        }
    }

    // Stop the payment controller
    stop() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        
        // Clear all active payment checkers
        this.paymentCheckers.clear();
        
        log('info', 'KHQR Payment Controller stopped');
    }

    // Get active checkers count (for monitoring)
    getActiveCheckersCount() {
        return this.paymentCheckers.size;
    }
}

module.exports = KHQRPaymentController;
