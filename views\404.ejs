<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 404 - Game Over | Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #000;
            color: #ffffff;
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Epic Cyberpunk Background */
        .cyber-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background:
                radial-gradient(ellipse at center, #0a0a0f 0%, #000 70%),
                linear-gradient(45deg, #000 0%, #0a0a0f 25%, #1a1a2e 50%, #16213e 75%, #000 100%);
            background-size: 100% 100%, 400% 400%;
            animation: cyberShift 25s ease infinite;
        }

        .cyber-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 15% 85%, rgba(255, 0, 128, 0.2) 0%, transparent 40%),
                radial-gradient(circle at 85% 15%, rgba(0, 255, 255, 0.2) 0%, transparent 40%),
                radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.15) 0%, transparent 30%);
            animation: cyberFloat 30s ease-in-out infinite;
        }

        .cyber-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 0, 128, 0.05) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: cyberGrid 60s linear infinite;
        }

        @keyframes cyberShift {
            0%, 100% { background-position: 0% 0%, 0% 50%; }
            25% { background-position: 0% 0%, 100% 50%; }
            50% { background-position: 0% 0%, 100% 100%; }
            75% { background-position: 0% 0%, 0% 100%; }
        }

        @keyframes cyberFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            33% { transform: translateY(-30px) rotate(2deg) scale(1.05); }
            66% { transform: translateY(15px) rotate(-2deg) scale(0.95); }
        }

        @keyframes cyberGrid {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Glitch Effects */
        .glitch {
            position: relative;
            animation: glitch 2s infinite;
        }

        .glitch::before,
        .glitch::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .glitch::before {
            animation: glitch-1 0.5s infinite;
            color: #ff0080;
            z-index: -1;
        }

        .glitch::after {
            animation: glitch-2 0.5s infinite;
            color: #00ffff;
            z-index: -2;
        }

        @keyframes glitch {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(-2px, 2px); }
            40% { transform: translate(-2px, -2px); }
            60% { transform: translate(2px, 2px); }
            80% { transform: translate(2px, -2px); }
        }

        @keyframes glitch-1 {
            0%, 100% { transform: translate(0); }
            10% { transform: translate(-2px, -2px); }
            20% { transform: translate(2px, 2px); }
            30% { transform: translate(-2px, 2px); }
            40% { transform: translate(2px, -2px); }
        }

        @keyframes glitch-2 {
            0%, 100% { transform: translate(0); }
            10% { transform: translate(2px, 2px); }
            20% { transform: translate(-2px, -2px); }
            30% { transform: translate(2px, -2px); }
            40% { transform: translate(-2px, 2px); }
        }

        /* Main Container */
        .error-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }

        .error-card {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(30px);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 25px;
            padding: 4rem 3rem;
            text-align: center;
            max-width: 600px;
            position: relative;
            overflow: hidden;
            box-shadow:
                0 0 50px rgba(0, 255, 255, 0.2),
                inset 0 0 50px rgba(255, 0, 128, 0.1);
        }

        .error-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 128, 0.1));
            z-index: -1;
        }

        /* Gaming Controller Icon */
        .game-over-icon {
            font-size: 8rem;
            background: linear-gradient(135deg, #ff0080, #00ffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 2rem;
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Error Code */
        .error-code {
            font-family: 'Orbitron', monospace;
            font-size: 8rem;
            font-weight: 900;
            background: linear-gradient(135deg, #ff0080, #00ffff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            text-shadow:
                0 0 20px rgba(255, 0, 128, 0.5),
                0 0 40px rgba(0, 255, 255, 0.3);
            animation: neonPulse 2s ease-in-out infinite alternate;
        }

        @keyframes neonPulse {
            from {
                filter: brightness(1) saturate(1);
                text-shadow:
                    0 0 20px rgba(255, 0, 128, 0.5),
                    0 0 40px rgba(0, 255, 255, 0.3);
            }
            to {
                filter: brightness(1.2) saturate(1.5);
                text-shadow:
                    0 0 30px rgba(255, 0, 128, 0.8),
                    0 0 60px rgba(0, 255, 255, 0.6);
            }
        }

        /* Game Over Text */
        .game-over-text {
            font-family: 'Orbitron', monospace;
            font-size: 3rem;
            font-weight: 700;
            color: #ff0080;
            margin-bottom: 1rem;
            text-shadow:
                0 0 10px rgba(255, 0, 128, 0.8),
                0 0 20px rgba(255, 0, 128, 0.4);
        }

        .error-message {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        /* Epic Buttons */
        .cyber-btn {
            background: linear-gradient(135deg, #00ffff, #ff0080);
            border: none;
            border-radius: 15px;
            padding: 1rem 2.5rem;
            font-weight: 700;
            font-size: 1.1rem;
            color: #000;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cyber-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .cyber-btn:hover {
            transform: translateY(-3px);
            box-shadow:
                0 10px 25px rgba(0, 255, 255, 0.4),
                0 0 30px rgba(255, 0, 128, 0.3);
            color: #000;
        }

        .cyber-btn:hover::before {
            left: 100%;
        }

        .cyber-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(0, 255, 255, 0.5);
            color: #00ffff;
        }

        .cyber-btn-secondary:hover {
            background: rgba(0, 255, 255, 0.2);
            color: #00ffff;
            box-shadow:
                0 10px 25px rgba(0, 255, 255, 0.3),
                inset 0 0 20px rgba(0, 255, 255, 0.1);
        }

        /* Floating Particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .particle:nth-child(odd) {
            background: #ff0080;
            animation-delay: -2s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0;
            }
            10%, 90% {
                opacity: 1;
            }
            50% {
                transform: translateY(-100px) translateX(50px) scale(1.5);
                opacity: 0.8;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }

            .game-over-text {
                font-size: 2rem;
            }

            .game-over-icon {
                font-size: 5rem;
            }

            .error-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="cyber-bg"></div>

    <!-- Floating Particles -->
    <div class="particle" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 20%; left: 80%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 60%; left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>
    <div class="particle" style="top: 30%; left: 90%; animation-delay: 4s;"></div>
    <div class="particle" style="top: 70%; left: 5%; animation-delay: 5s;"></div>

    <div class="error-container">
        <div class="error-card">
            <!-- Gaming Controller Icon -->
            <div class="game-over-icon">
                <i class="bi bi-controller"></i>
            </div>

            <!-- Glitch 404 Code -->
            <div class="error-code glitch" data-text="404">404</div>

            <!-- Game Over Text -->
            <h1 class="game-over-text glitch" data-text="GAME OVER">GAME OVER</h1>

            <!-- Error Message -->
            <p class="error-message">
                🎮 <strong>Connection Lost!</strong><br>
                The page you're looking for has been disconnected from the server.<br>
                Maybe it was sold to another player? 😄
            </p>

            <!-- Epic Action Buttons -->
            <div class="action-buttons">
                <a href="/" class="cyber-btn">
                    <i class="bi bi-house-fill"></i>
                    Respawn Home
                </a>
                <a href="/admin" class="cyber-btn">
                    <i class="bi bi-speedometer2"></i>
                    Admin Panel
                </a>
                <button onclick="history.back()" class="cyber-btn cyber-btn-secondary">
                    <i class="bi bi-arrow-left"></i>
                    Go Back
                </button>
            </div>

            <!-- Gaming Stats -->
            <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid rgba(0, 255, 255, 0.3);">
                <div style="display: flex; justify-content: space-around; text-align: center;">
                    <div>
                        <div style="color: #00ffff; font-weight: 700; font-size: 1.5rem;">404</div>
                        <div style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">Error Code</div>
                    </div>
                    <div>
                        <div style="color: #ff0080; font-weight: 700; font-size: 1.5rem;">∞</div>
                        <div style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">Retry Attempts</div>
                    </div>
                    <div>
                        <div style="color: #00ff80; font-weight: 700; font-size: 1.5rem;">0</div>
                        <div style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem;">Pages Found</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Epic Sound Effect (optional)
        function playGameOverSound() {
            // You can add a game over sound effect here
            console.log("🎮 Game Over! Page not found.");
        }

        // Glitch effect enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const glitchElements = document.querySelectorAll('.glitch');

            glitchElements.forEach(element => {
                setInterval(() => {
                    element.style.textShadow = `
                        ${Math.random() * 10 - 5}px ${Math.random() * 10 - 5}px 0 #ff0080,
                        ${Math.random() * 10 - 5}px ${Math.random() * 10 - 5}px 0 #00ffff
                    `;

                    setTimeout(() => {
                        element.style.textShadow = '';
                    }, 100);
                }, 3000 + Math.random() * 2000);
            });

            // Play sound effect
            playGameOverSound();
        });

        // Konami Code Easter Egg
        let konamiCode = [];
        const konamiSequence = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65]; // ↑↑↓↓←→←→BA

        document.addEventListener('keydown', function(e) {
            konamiCode.push(e.keyCode);
            if (konamiCode.length > konamiSequence.length) {
                konamiCode.shift();
            }

            if (konamiCode.join(',') === konamiSequence.join(',')) {
                // Easter egg activated!
                document.body.style.animation = 'cyberFloat 2s ease-in-out';
                alert('🎮 CHEAT CODE ACTIVATED! But the page is still missing... 😄');
                konamiCode = [];
            }
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
