{"name": "telegram-gaming-accounts-bot", "version": "1.0.0", "description": "Telegram bot for selling gaming accounts with KHQR payment processing", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "init-db": "node scripts/init-db.js"}, "keywords": ["telegram", "bot", "gaming", "accounts", "khqr", "payment"], "author": "Gaming Accounts <PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "node-telegram-bot-api": "^0.64.0", "express-session": "^1.17.3", "bcrypt": "^5.1.1", "ejs": "^3.1.9", "axios": "^1.6.2", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "multer": "^1.4.5-lts.1"}, "engines": {"node": ">=16.0.0"}}