<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Details - Gaming Accounts Store Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .breadcrumb {
            color: #666;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .account-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .account-title {
            flex: 1;
        }

        .account-title h2 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
        }

        .account-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .meta-item {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-available {
            background: #d4edda;
            color: #155724;
        }

        .status-sold {
            background: #f8d7da;
            color: #721c24;
        }

        .price-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            min-width: 150px;
        }

        .account-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .detail-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
        }

        .detail-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-item {
            margin-bottom: 1rem;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            color: #333;
            word-break: break-word;
        }

        .actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .images-section {
            margin-top: 2rem;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-item {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .account-header {
                flex-direction: column;
                align-items: stretch;
            }

            .price-tag {
                text-align: left;
            }

            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="bi bi-controller"></i> Account Details</h1>
            <div class="breadcrumb">
                <a href="/admin/dashboard">Dashboard</a> / 
                <a href="/admin/accounts">Accounts</a> / 
                <%= account.title %>
            </div>
        </div>

        <div class="account-card">
            <div class="account-header">
                <div class="account-title">
                    <h2><%= account.title %></h2>
                    <div class="account-meta">
                        <div class="meta-item">
                            <i class="bi bi-tag"></i> ID: <%= account.id %>
                        </div>
                        <div class="meta-item">
                            <i class="bi bi-calendar"></i> Created: <%= formatDate(account.created_date) %>
                        </div>
                        <div class="status-badge <%= account.status === 'available' ? 'status-available' : 'status-sold' %>">
                            <%= account.status %>
                        </div>
                    </div>
                </div>
                <div class="price-tag">
                    <%= formatCurrency(account.price) %>
                </div>
            </div>

            <div class="account-details">
                <div class="detail-section">
                    <h3><i class="bi bi-info-circle"></i> Basic Information</h3>
                    <div class="detail-item">
                        <div class="detail-label">Game Type</div>
                        <div class="detail-value"><%= account.game_type %></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Title</div>
                        <div class="detail-value"><%= account.title %></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Price</div>
                        <div class="detail-value"><%= formatCurrency(account.price) %></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Status</div>
                        <div class="detail-value"><%= account.status %></div>
                    </div>
                </div>

                <div class="detail-section">
                    <h3><i class="bi bi-file-text"></i> Description</h3>
                    <div class="detail-value">
                        <%= account.description || 'No description provided' %>
                    </div>
                </div>

                <% if (account.credentials) { %>
                <div class="detail-section">
                    <h3><i class="bi bi-key"></i> Credentials</h3>
                    <div class="detail-value" style="font-family: monospace; background: #fff; padding: 1rem; border-radius: 8px;">
                        <%= account.credentials %>
                    </div>
                </div>
                <% } %>
            </div>

            <div class="actions">
                <a href="/admin/accounts/<%= account.id %>/edit" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Edit Account
                </a>
                <a href="/admin/accounts/<%= account.id %>/showcase" class="btn btn-secondary">
                    <i class="bi bi-display"></i> View Showcase
                </a>
                <button onclick="deleteAccount(<%= account.id %>)" class="btn btn-danger">
                    <i class="bi bi-trash"></i> Delete Account
                </button>
                <a href="/admin/accounts" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Accounts
                </a>
            </div>
        </div>
    </div>

    <script>
        function deleteAccount(accountId) {
            if (confirm('Are you sure you want to delete this gaming account? This action cannot be undone.')) {
                fetch(`/admin/accounts/${accountId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Account deleted successfully');
                        window.location.href = '/admin/accounts';
                    } else {
                        alert(data.error || 'Failed to delete account');
                    }
                })
                .catch(error => {
                    console.error('Error deleting account:', error);
                    alert('Failed to delete account');
                });
            }
        }
    </script>
</body>
</html>
