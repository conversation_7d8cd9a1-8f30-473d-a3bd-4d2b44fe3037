<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Epic Animated Background */
        .dashboard-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        .dashboard-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 128, 0.05) 0%, transparent 50%);
            animation: float 25s ease-in-out infinite;
        }

        .dashboard-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 212, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.02) 1px, transparent 1px);
            background-size: 60px 60px;
            animation: gridMove 40s linear infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(60px, 60px); }
        }

        /* Epic Sidebar */
        .epic-sidebar {
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(0, 212, 255, 0.2);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .epic-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(0, 212, 255, 0.05) 0%, transparent 100%);
            pointer-events: none;
        }

        .sidebar-brand {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(0, 212, 255, 0.1);
            position: relative;
        }

        .brand-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            color: inherit;
        }

        .brand-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #ff00ff);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #ffffff;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
        }

        .brand-text h4 {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(135deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-text span {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.4);
            padding: 0 1.5rem;
            margin-bottom: 0.5rem;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            background: rgba(0, 212, 255, 0.1);
            color: #00d4ff;
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0, 212, 255, 0.2);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(255, 0, 255, 0.1));
            color: #00d4ff;
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
        }

        .nav-link i {
            font-size: 1.1rem;
            width: 20px;
            transition: all 0.3s ease;
        }

        .nav-link:hover i {
            transform: scale(1.1);
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            padding: 2rem;
            position: relative;
        }

        /* Epic Header */
        .epic-header {
            background: rgba(10, 10, 15, 0.8);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 25px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .epic-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(255, 0, 255, 0.05));
            pointer-events: none;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-title i {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title h1 {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .settings-section {
            background: rgba(10, 10, 15, 0.8);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .settings-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.03), rgba(255, 0, 255, 0.03));
            pointer-events: none;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            position: relative;
            z-index: 1;
        }

        .section-title i {
            font-size: 1.3rem;
            background: linear-gradient(135deg, #00d4ff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.1);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item:hover {
            background: rgba(0, 212, 255, 0.05);
            border-radius: 12px;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .setting-label {
            font-weight: 600;
            color: #ffffff;
            font-size: 1rem;
        }

        .setting-description {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 0.25rem;
            line-height: 1.4;
        }

        .setting-value {
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            background: rgba(0, 212, 255, 0.1);
            padding: 0.75rem 1.25rem;
            border-radius: 12px;
            color: #00d4ff;
            border: 1px solid rgba(0, 212, 255, 0.3);
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge.online {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status-badge.offline {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-badge i {
            font-size: 0.7rem;
        }

        /* Button Styling */
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #ffffff;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #ff9800);
            color: #000;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #ff9800, #ffc107);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: #ffffff;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: #ffffff;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333, #dc3545);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.75rem;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
            }

            .epic-sidebar {
                transform: translateX(-100%);
            }
        }

        @media (max-width: 768px) {
            .epic-header {
                padding: 1.5rem;
            }

            .settings-section {
                padding: 1.5rem;
            }

            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-bg"></div>

    <!-- Epic Sidebar -->
    <div class="epic-sidebar">
        <div class="sidebar-brand">
            <a href="/" class="brand-logo">
                <div class="brand-icon">
                    <i class="bi bi-controller"></i>
                </div>
                <div class="brand-text">
                    <h4>Gaming Store</h4>
                    <span>Admin Panel</span>
                </div>
            </a>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="/admin/dashboard" class="nav-link">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <div class="nav-item">
                    <a href="/admin/users" class="nav-link">
                        <i class="bi bi-people"></i>
                        <span>Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/accounts" class="nav-link">
                        <i class="bi bi-controller"></i>
                        <span>Gaming Accounts</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/transactions" class="nav-link">
                        <i class="bi bi-credit-card"></i>
                        <span>Transactions</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/purchases" class="nav-link">
                        <i class="bi bi-bag-check"></i>
                        <span>Purchases</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <div class="nav-item">
                    <a href="/admin/admin-users" class="nav-link">
                        <i class="bi bi-shield-lock"></i>
                        <span>Admin Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/broadcast" class="nav-link">
                        <i class="bi bi-megaphone"></i>
                        <span>Broadcast</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/health" class="nav-link">
                        <i class="bi bi-heart-pulse"></i>
                        <span>System Health</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/settings" class="nav-link active">
                        <i class="bi bi-gear"></i>
                        <span>Settings</span>
                    </a>
                </div>
                <div class="nav-item">
                    <form action="/admin/logout" method="POST" style="margin: 0;">
                        <button type="submit" class="nav-link" style="background: none; border: none; width: 100%; text-align: left;">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Epic Header -->
        <div class="epic-header">
            <div class="header-content">
                <div class="header-title">
                    <i class="bi bi-gear"></i>
                    <h1>Settings</h1>
                </div>
            </div>
        </div>

        <!-- Bot Configuration -->
        <div class="settings-section">
            <div class="section-title">
                <i class="bi bi-robot"></i>
                Telegram Bot Configuration
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Bot Token</div>
                    <div class="setting-description">Telegram Bot API token for bot communication</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="setting-value" id="botTokenValue"><%= settings.botTokenMasked %></div>
                    <button class="btn btn-primary btn-sm" onclick="changeBotToken()">
                        <i class="bi bi-pencil"></i>
                        Change
                    </button>
                </div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Bot Status</div>
                    <div class="setting-description">Current bot connection status</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <span class="status-badge <%= settings.botStatus === 'online' ? 'online' : 'offline' %>" id="botStatus">
                        <i class="bi bi-<%= settings.botStatus === 'online' ? 'check-circle-fill' : 'x-circle-fill' %>"></i>
                        <%= settings.botStatus === 'online' ? 'Online' : 'Offline' %>
                    </span>
                    <button class="btn btn-warning btn-sm" onclick="toggleBotStatus()">
                        <i class="bi bi-power"></i>
                        Toggle
                    </button>
                </div>
            </div>
        </div>

        <!-- Payment Configuration -->
        <div class="settings-section">
            <div class="section-title">
                <i class="bi bi-credit-card"></i>
                KHQR Payment Configuration
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Bakong ID</div>
                    <div class="setting-description">KHQR Bakong account identifier</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="setting-value" id="bakongIdValue"><%= settings.khqrBakongId %></div>
                    <button class="btn btn-primary btn-sm" onclick="editSetting('bakongId', 'Bakong ID')">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </button>
                </div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Merchant Name</div>
                    <div class="setting-description">Display name for payment transactions</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="setting-value" id="merchantNameValue"><%= settings.khqrMerchantName %></div>
                    <button class="btn btn-primary btn-sm" onclick="editSetting('merchantName', 'Merchant Name')">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </button>
                </div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Bearer Token</div>
                    <div class="setting-description">JWT authentication token for KHQR API</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="setting-value" id="bearerTokenValue"><%= settings.bearerTokenMasked %></div>
                    <button class="btn btn-primary btn-sm" onclick="editSetting('bearerToken', 'Bearer Token')">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </button>
                </div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Minimum Topup</div>
                    <div class="setting-description">Minimum amount users can topup</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="setting-value" id="minTopupValue">$<%= settings.minTopupAmount %></div>
                    <button class="btn btn-primary btn-sm" onclick="editSetting('minTopup', 'Minimum Topup Amount')">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </button>
                </div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Maximum Topup</div>
                    <div class="setting-description">Maximum amount users can topup</div>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <div class="setting-value" id="maxTopupValue">$<%= settings.maxTopupAmount %></div>
                    <button class="btn btn-primary btn-sm" onclick="editSetting('maxTopup', 'Maximum Topup Amount')">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </button>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="settings-section">
            <div class="section-title">
                <i class="bi bi-info-circle"></i>
                System Information
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Environment</div>
                    <div class="setting-description">Current application environment</div>
                </div>
                <div class="setting-value"><%= process.env.NODE_ENV || 'development' %></div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Server Port</div>
                    <div class="setting-description">Port the server is running on</div>
                </div>
                <div class="setting-value"><%= process.env.PORT || '3000' %></div>
            </div>
            <div class="setting-item">
                <div>
                    <div class="setting-label">Database</div>
                    <div class="setting-description">Database connection status</div>
                </div>
                <span class="status-badge online">
                    <i class="bi bi-database-check"></i>
                    Connected
                </span>
            </div>
        </div>
    </div>

    <!-- Custom Edit Modal -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background: rgba(10, 10, 15, 0.95); backdrop-filter: blur(25px); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: 20px;">
                <div class="modal-header" style="border-bottom: 1px solid rgba(0, 212, 255, 0.2);">
                    <h5 class="modal-title" id="editModalLabel" style="color: #ffffff; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="bi bi-pencil-square" style="background: linear-gradient(135deg, #00d4ff, #ff00ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"></i>
                        Edit Setting
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 2rem;">
                    <form id="editForm">
                        <div class="mb-3">
                            <label for="settingValue" class="form-label" id="settingLabel" style="color: #ffffff; font-weight: 600; margin-bottom: 0.75rem;">Setting Value</label>
                            <div class="input-group">
                                <span class="input-group-text" id="settingIcon" style="background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); color: #00d4ff;">
                                    <i class="bi bi-gear"></i>
                                </span>
                                <input type="text" class="form-control" id="settingValue" placeholder="Enter new value"
                                       style="background: rgba(0, 212, 255, 0.05); border: 1px solid rgba(0, 212, 255, 0.3); color: #ffffff; border-radius: 0 8px 8px 0;">
                            </div>
                            <div class="form-text" id="settingHelp" style="color: rgba(255, 255, 255, 0.6); margin-top: 0.5rem;">Enter the new value for this setting</div>
                        </div>
                        <div class="alert d-none" id="settingInfo" style="background: rgba(0, 212, 255, 0.1); border: 1px solid rgba(0, 212, 255, 0.3); color: #00d4ff; border-radius: 12px;">
                            <i class="bi bi-info-circle me-2"></i>
                            <span id="settingInfoText"></span>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 1px solid rgba(0, 212, 255, 0.2); padding: 1.5rem 2rem;">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="background: rgba(108, 117, 125, 0.2); border: 1px solid rgba(108, 117, 125, 0.3); color: #ffffff;">
                        <i class="bi bi-x-circle me-1"></i>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-primary" id="saveSettingBtn">
                        <i class="bi bi-check-circle me-1"></i>
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Show notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            // Add notification styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'rgba(34, 197, 94, 0.9)' : type === 'error' ? 'rgba(239, 68, 68, 0.9)' : 'rgba(0, 212, 255, 0.9)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                backdrop-filter: blur(10px);
                border: 1px solid ${type === 'success' ? 'rgba(34, 197, 94, 0.3)' : type === 'error' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(0, 212, 255, 0.3)'};
                z-index: 10000;
                animation: slideIn 0.3s ease;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            `;

            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Add notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                font-weight: 600;
            }
        `;
        document.head.appendChild(style);

        // Store the actual token values (hidden from display)
        const actualTokens = {
            botToken: '<%= fullTokens.botToken.replace(/'/g, "\\'") %>',
            bearerToken: '<%= fullTokens.bearerToken ? fullTokens.bearerToken.replace(/'/g, "\\'") : "" %>'
        };

        // Change Bot Token
        function changeBotToken() {
            openEditModal('botToken', 'Bot Token', actualTokens.botToken, 'bi-robot', 'Enter your Telegram Bot API token from @BotFather');
        }

        // Toggle Bot Status
        function toggleBotStatus() {
            const statusElement = document.getElementById('botStatus');
            const isOnline = statusElement.classList.contains('online');

            if (confirm(`Are you sure you want to ${isOnline ? 'stop' : 'start'} the bot?`)) {
                fetch('/admin/api/bot/toggle', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: isOnline ? 'stop' : 'start' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statusElement.className = `status-badge ${data.status}`;
                        statusElement.innerHTML = `
                            <i class="bi bi-${data.status === 'online' ? 'check-circle-fill' : 'x-circle-fill'}"></i>
                            ${data.status === 'online' ? 'Online' : 'Offline'}
                        `;
                        showNotification(`Bot ${data.status === 'online' ? 'started' : 'stopped'} successfully`, 'success');
                    } else {
                        showNotification(data.error || 'Failed to toggle bot status', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error toggling bot status', 'error');
                });
            }
        }

        // Edit Setting
        function editSetting(settingKey, settingName) {
            let currentValue;
            let icon = 'bi-gear';
            let helpText = `Enter the new value for ${settingName}`;

            // Handle special cases for different settings
            if (settingKey === 'bearerToken') {
                currentValue = actualTokens.bearerToken;
                icon = 'bi-key';
                helpText = 'Enter your JWT bearer token for KHQR API authentication';
            } else if (settingKey === 'bakongId') {
                const valueElement = document.getElementById(`${settingKey}Value`);
                currentValue = valueElement.textContent;
                icon = 'bi-bank';
                helpText = 'Enter your Bakong account ID (email format)';
            } else if (settingKey === 'merchantName') {
                const valueElement = document.getElementById(`${settingKey}Value`);
                currentValue = valueElement.textContent;
                icon = 'bi-shop';
                helpText = 'Enter the merchant name displayed on payment receipts';
            } else if (settingKey.includes('Topup')) {
                const valueElement = document.getElementById(`${settingKey}Value`);
                currentValue = valueElement.textContent.replace('$', '');
                icon = 'bi-currency-dollar';
                helpText = `Enter the ${settingKey.includes('min') ? 'minimum' : 'maximum'} topup amount in USD`;
            } else {
                const valueElement = document.getElementById(`${settingKey}Value`);
                currentValue = valueElement.textContent;
            }

            openEditModal(settingKey, settingName, currentValue, icon, helpText);
        }

        // Open Edit Modal
        function openEditModal(settingKey, settingName, currentValue, icon, helpText) {
            // Set modal content
            document.getElementById('editModalLabel').innerHTML = `
                <i class="bi ${icon}" style="background: linear-gradient(135deg, #00d4ff, #ff00ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"></i>
                Edit ${settingName}
            `;

            document.getElementById('settingLabel').textContent = settingName;
            document.getElementById('settingIcon').innerHTML = `<i class="bi ${icon}"></i>`;
            document.getElementById('settingValue').value = currentValue;
            document.getElementById('settingHelp').textContent = helpText;

            // Show info for specific settings
            const infoElement = document.getElementById('settingInfo');
            const infoTextElement = document.getElementById('settingInfoText');

            if (settingKey === 'bearerToken') {
                infoTextElement.textContent = 'This token will be used for all KHQR API requests. Make sure it\'s valid and not expired.';
                infoElement.classList.remove('d-none');
            } else if (settingKey === 'botToken') {
                infoTextElement.textContent = 'Get this token from @BotFather on Telegram. Keep it secure and never share it publicly.';
                infoElement.classList.remove('d-none');
            } else if (settingKey.includes('Topup')) {
                infoTextElement.textContent = 'This will affect the validation limits for user topup requests.';
                infoElement.classList.remove('d-none');
            } else {
                infoElement.classList.add('d-none');
            }

            // Store current setting info for save function
            window.currentEditSetting = { key: settingKey, name: settingName };

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editModal'));
            modal.show();

            // Focus on input
            setTimeout(() => {
                document.getElementById('settingValue').focus();
                document.getElementById('settingValue').select();
            }, 300);
        }

        // Handle save button click
        document.getElementById('saveSettingBtn').addEventListener('click', function() {
            const value = document.getElementById('settingValue').value.trim();
            const setting = window.currentEditSetting;

            if (!value) {
                showNotification('Please enter a value', 'error');
                return;
            }

            // Validate based on setting type
            if (setting.key.includes('Topup')) {
                if (isNaN(value) || parseFloat(value) <= 0) {
                    showNotification('Please enter a valid positive number', 'error');
                    return;
                }
                updateSetting(setting.key, parseFloat(value));
            } else if (setting.key === 'bearerToken') {
                // Update stored token
                actualTokens.bearerToken = value;
                updateSetting(setting.key, value);
            } else if (setting.key === 'botToken') {
                // Update stored token
                actualTokens.botToken = value;
                updateSetting(setting.key, value);
            } else {
                updateSetting(setting.key, value);
            }

            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
            modal.hide();
        });

        // Handle Enter key in input
        document.getElementById('settingValue').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('saveSettingBtn').click();
            }
        });

        // Update Setting
        function updateSetting(key, value) {
            fetch('/admin/api/settings/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ key, value })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the display value
                    const valueElement = document.getElementById(`${key}Value`);
                    if (valueElement) {
                        if (key.includes('Topup')) {
                            valueElement.textContent = `$${value}`;
                        } else if (key === 'botToken') {
                            // Show masked bot token
                            valueElement.textContent = value.length > 4 ? '***' + value.slice(-4) : value;
                        } else if (key === 'bearerToken') {
                            // Show masked bearer token
                            valueElement.textContent = value.length > 8 ? '***' + value.slice(-8) : value;
                        } else {
                            valueElement.textContent = value;
                        }
                    }
                    showNotification(`${key} updated successfully`, 'success');
                } else {
                    showNotification(data.error || 'Failed to update setting', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error updating setting', 'error');
            });
        }
    </script>
</body>
</html>
