<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transactions Management - Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        /* Epic Animated Background */
        .dashboard-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }
        
        .dashboard-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 128, 0.05) 0%, transparent 50%);
            animation: float 25s ease-in-out infinite;
        }
        
        .dashboard-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 212, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.02) 1px, transparent 1px);
            background-size: 60px 60px;
            animation: gridMove 40s linear infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }
        
        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(60px, 60px); }
        }
        
        /* Epic Sidebar */
        .epic-sidebar {
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(0, 212, 255, 0.2);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .epic-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, 
                rgba(0, 212, 255, 0.05) 0%, 
                rgba(255, 0, 128, 0.05) 50%, 
                rgba(0, 212, 255, 0.05) 100%);
            pointer-events: none;
        }
        
        /* Sidebar Brand */
        .sidebar-brand {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }
        
        .brand-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
        }
        
        .brand-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .brand-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 14px;
            opacity: 0;
            filter: blur(8px);
            transition: opacity 0.3s ease;
            animation: brandPulse 3s ease-in-out infinite;
        }
        
        @keyframes brandPulse {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.4; }
        }
        
        .brand-icon i {
            font-size: 1.5rem;
            color: white;
            z-index: 2;
        }
        
        .brand-text h4 {
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            font-size: 1.3rem;
        }
        
        .brand-text span {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        /* Sidebar Navigation */
        .sidebar-nav {
            padding: 1rem 0;
            position: relative;
            z-index: 2;
        }
        
        .nav-section {
            margin-bottom: 2rem;
        }
        
        .nav-section-title {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 1.5rem;
            margin-bottom: 1rem;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-radius: 0;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }
        
        /* Epic Header */
        .epic-header {
            background: rgba(10, 10, 15, 0.9);
            backdrop-filter: blur(25px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 2rem 3rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-title h1 {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }
        
        .header-title i {
            font-size: 2rem;
            color: #00d4ff;
        }
        
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .action-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="dashboard-bg"></div>
    
    <!-- Epic Sidebar -->
    <div class="epic-sidebar">
        <div class="sidebar-brand">
            <a href="/" class="brand-logo">
                <div class="brand-icon">
                    <i class="bi bi-controller"></i>
                </div>
                <div class="brand-text">
                    <h4>Gaming Store</h4>
                    <span>Admin Panel</span>
                </div>
            </a>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="/admin/dashboard" class="nav-link">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <div class="nav-item">
                    <a href="/admin/users" class="nav-link">
                        <i class="bi bi-people"></i>
                        <span>Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/accounts" class="nav-link">
                        <i class="bi bi-controller"></i>
                        <span>Gaming Accounts</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/transactions" class="nav-link active">
                        <i class="bi bi-credit-card"></i>
                        <span>Transactions</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/purchases" class="nav-link">
                        <i class="bi bi-bag-check"></i>
                        <span>Purchases</span>
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <div class="nav-item">
                    <a href="/admin/admin-users" class="nav-link">
                        <i class="bi bi-shield-lock"></i>
                        <span>Admin Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/broadcast" class="nav-link">
                        <i class="bi bi-megaphone"></i>
                        <span>Broadcast</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/settings" class="nav-link">
                        <i class="bi bi-gear"></i>
                        <span>Settings</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/health" class="nav-link">
                        <i class="bi bi-heart-pulse"></i>
                        <span>System Status</span>
                    </a>
                </div>
                <div class="nav-item">
                    <form action="/admin/logout" method="POST" style="margin: 0;">
                        <button type="submit" class="nav-link" style="background: none; border: none; width: 100%; text-align: left;">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Epic Header -->
        <div class="epic-header">
            <div class="header-content">
                <div class="header-title">
                    <i class="bi bi-credit-card"></i>
                    <h1>Transactions</h1>
                </div>
                <div class="header-actions">
                    <a href="#" class="action-btn" onclick="refreshTransactions()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>Refresh</span>
                    </a>
                    <a href="#" class="action-btn" onclick="exportTransactions()">
                        <i class="bi bi-download"></i>
                        <span>Export</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Transactions Content -->
        <div class="transactions-section">
            <!-- Stats Overview -->
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= transactionStats.total || 0 %></h3>
                        <p>Total Transactions</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= transactionStats.completed || 0 %></h3>
                        <p>Completed</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= transactionStats.pending || 0 %></h3>
                        <p>Pending</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= formatCurrency(transactionStats.totalRevenue || 0) %></h3>
                        <p>Total Volume</p>
                    </div>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="search-section">
                <div class="search-bar">
                    <i class="bi bi-search"></i>
                    <input type="text" id="transactionSearch" placeholder="Search transactions by user, amount, or transaction ID...">
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Transactions</button>
                    <button class="filter-btn" data-filter="completed">Completed</button>
                    <button class="filter-btn" data-filter="pending">Pending</button>
                    <button class="filter-btn" data-filter="failed">Failed</button>
                </div>
            </div>

            <!-- Transactions Grid -->
            <div class="transactions-grid" id="transactionsGrid">
                <% if (transactions && transactions.length > 0) { %>
                    <% transactions.forEach(function(transaction) { %>
                        <div class="transaction-card" data-status="<%= transaction.status %>">
                            <div class="transaction-header">
                                <div class="transaction-type">
                                    <% if (transaction.type === 'topup') { %>
                                        <i class="bi bi-plus-circle text-success"></i>
                                        <span>Top Up</span>
                                    <% } else if (transaction.type === 'purchase') { %>
                                        <i class="bi bi-bag-check text-primary"></i>
                                        <span>Purchase</span>
                                    <% } else if (transaction.type === 'admin_credit') { %>
                                        <i class="bi bi-shield-plus text-warning"></i>
                                        <span>Admin Credit</span>
                                    <% } else if (transaction.type === 'admin_debit') { %>
                                        <i class="bi bi-shield-minus text-danger"></i>
                                        <span>Admin Debit</span>
                                    <% } else { %>
                                        <i class="bi bi-credit-card text-info"></i>
                                        <span><%= transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1) %></span>
                                    <% } %>
                                </div>
                                <div class="transaction-status">
                                    <% if (transaction.status === 'completed') { %>
                                        <span class="status-badge status-completed">
                                            <i class="bi bi-check-circle"></i>
                                            Completed
                                        </span>
                                    <% } else if (transaction.status === 'pending') { %>
                                        <span class="status-badge status-pending">
                                            <i class="bi bi-clock"></i>
                                            Pending
                                        </span>
                                    <% } else if (transaction.status === 'expired') { %>
                                        <span class="status-badge status-expired">
                                            <i class="bi bi-x-circle"></i>
                                            Expired
                                        </span>
                                    <% } else { %>
                                        <span class="status-badge status-failed">
                                            <i class="bi bi-exclamation-circle"></i>
                                            <%= transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1) %>
                                        </span>
                                    <% } %>
                                </div>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-amount">
                                    <% if (transaction.amount >= 0) { %>
                                        <span class="amount-positive">+<%= formatCurrency(transaction.amount) %></span>
                                    <% } else { %>
                                        <span class="amount-negative"><%= formatCurrency(transaction.amount) %></span>
                                    <% } %>
                                </div>
                                <div class="transaction-info">
                                    <div class="info-row">
                                        <span class="info-label">User:</span>
                                        <span class="info-value">
                                            <% if (transaction.first_name || transaction.last_name) { %>
                                                <div class="user-display">
                                                    <div class="user-name"><%= (transaction.first_name || '') + ' ' + (transaction.last_name || '') %></div>
                                                    <% if (transaction.username) { %>
                                                        <div class="user-username">@<%= transaction.username %></div>
                                                    <% } %>
                                                </div>
                                            <% } else if (transaction.username) { %>
                                                @<%= transaction.username %>
                                            <% } else { %>
                                                ID: <%= transaction.user_id %>
                                            <% } %>
                                        </span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Date:</span>
                                        <span class="info-value"><%= formatDate(transaction.timestamp) %></span>
                                    </div>
                                    <% if (transaction.transaction_id) { %>
                                        <div class="info-row">
                                            <span class="info-label">ID:</span>
                                            <span class="info-value"><%= transaction.transaction_id %></span>
                                        </div>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% }); %>
                <% } else { %>
                    <div class="empty-state">
                        <i class="bi bi-credit-card"></i>
                        <h3>No Transactions Found</h3>
                        <p>No transactions have been processed yet. Transactions will appear here when users make payments through the Telegram bot.</p>
                    </div>
                <% } %>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Epic Transactions JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stat cards
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateX(-30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateX(0)';
                }, index * 100);
            });

            // Search functionality
            const searchInput = document.getElementById('transactionSearch');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                filterTransactions(searchTerm);
            });

            // Filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    const filter = this.dataset.filter;
                    filterTransactionsByStatus(filter);
                });
            });
        });

        function filterTransactions(searchTerm) {
            const transactionCards = document.querySelectorAll('.transaction-card');
            transactionCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function filterTransactionsByStatus(filter) {
            const transactionCards = document.querySelectorAll('.transaction-card');
            transactionCards.forEach(card => {
                const status = card.dataset.status;

                if (filter === 'all') {
                    card.style.display = 'block';
                } else if (filter === 'completed' && status === 'completed') {
                    card.style.display = 'block';
                } else if (filter === 'pending' && status === 'pending') {
                    card.style.display = 'block';
                } else if (filter === 'failed' && status === 'failed') {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }

                if (card.style.display === 'block') {
                    card.style.animation = 'fadeIn 0.3s ease';
                }
            });
        }

        function refreshTransactions() {
            const refreshBtn = document.querySelector('.action-btn');
            const originalText = refreshBtn.innerHTML;

            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i><span>Refreshing...</span>';
            refreshBtn.style.pointerEvents = 'none';

            showNotification('Refreshing transactions list...', 'info');

            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
                refreshBtn.style.pointerEvents = 'auto';
                showNotification('Transactions list refreshed!', 'success');
            }, 1500);
        }

        function exportTransactions() {
            showNotification('Preparing transactions export...', 'info');
            setTimeout(() => {
                showNotification('Export completed successfully!', 'success');
            }, 2000);
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 2rem;
                right: 2rem;
                background: ${type === 'success' ? 'rgba(34, 197, 94, 0.9)' : type === 'error' ? 'rgba(239, 68, 68, 0.9)' : 'rgba(59, 130, 246, 0.9)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                z-index: 10000;
                backdrop-filter: blur(10px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .spin {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* Transactions Section Styles */
            .transactions-section {
                padding: 3rem;
            }

            .stats-overview {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                margin-bottom: 3rem;
            }

            .stat-card {
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(25px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 1.5rem;
                display: flex;
                align-items: center;
                gap: 1rem;
                transition: all 0.3s ease;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                border-color: rgba(0, 212, 255, 0.3);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            }

            .stat-icon {
                width: 50px;
                height: 50px;
                background: linear-gradient(135deg, #00d4ff, #ff0080);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.5rem;
                color: white;
            }

            .stat-info h3 {
                font-size: 1.8rem;
                font-weight: 800;
                background: linear-gradient(135deg, #00d4ff, #ff0080);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin: 0;
            }

            .stat-info p {
                color: rgba(255, 255, 255, 0.7);
                margin: 0;
                font-weight: 500;
            }

            .search-section {
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(25px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 2rem;
                margin-bottom: 2rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .search-bar {
                position: relative;
                flex: 1;
                max-width: 400px;
            }

            .search-bar i {
                position: absolute;
                left: 1rem;
                top: 50%;
                transform: translateY(-50%);
                color: rgba(255, 255, 255, 0.5);
                z-index: 2;
            }

            .search-bar input {
                width: 100%;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 0.75rem 1rem 0.75rem 3rem;
                color: white;
                font-size: 1rem;
                transition: all 0.3s ease;
            }

            .search-bar input::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            .search-bar input:focus {
                outline: none;
                border-color: #00d4ff;
                background: rgba(0, 212, 255, 0.1);
                box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
            }

            .filter-buttons {
                display: flex;
                gap: 0.5rem;
            }

            .filter-btn {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: rgba(255, 255, 255, 0.7);
                padding: 0.5rem 1rem;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .filter-btn:hover,
            .filter-btn.active {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
                color: #00d4ff;
            }

            .transactions-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                gap: 1.5rem;
            }

            .transaction-card {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 1.5rem;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
            }

            .transaction-card:hover {
                transform: translateY(-5px);
                border-color: rgba(0, 212, 255, 0.3);
                box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
            }

            .transaction-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            }

            .transaction-type {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 600;
            }

            .transaction-type i {
                font-size: 1.2rem;
            }

            .status-badge {
                display: flex;
                align-items: center;
                gap: 0.3rem;
                padding: 0.3rem 0.8rem;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 500;
            }

            .status-completed {
                background: rgba(34, 197, 94, 0.2);
                color: #22c55e;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .status-pending {
                background: rgba(251, 191, 36, 0.2);
                color: #fbbf24;
                border: 1px solid rgba(251, 191, 36, 0.3);
            }

            .status-expired {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .status-failed {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            /* Transaction Type Styling */
            .transaction-type {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem 1rem;
                border-radius: 10px;
                font-weight: 600;
                margin-bottom: 1rem;
            }

            .transaction-type.topup {
                background: rgba(34, 197, 94, 0.2);
                color: #22c55e;
                border: 1px solid rgba(34, 197, 94, 0.3);
            }

            .transaction-type.purchase {
                background: rgba(59, 130, 246, 0.2);
                color: #3b82f6;
                border: 1px solid rgba(59, 130, 246, 0.3);
            }

            .transaction-type.admin_credit {
                background: rgba(251, 191, 36, 0.2);
                color: #fbbf24;
                border: 1px solid rgba(251, 191, 36, 0.3);
            }

            .transaction-type.admin_debit {
                background: rgba(239, 68, 68, 0.2);
                color: #ef4444;
                border: 1px solid rgba(239, 68, 68, 0.3);
            }

            .transaction-details {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .transaction-amount {
                font-size: 1.5rem;
                font-weight: 700;
                text-align: center;
                padding: 1rem;
                border-radius: 10px;
                background: rgba(255, 255, 255, 0.05);
            }

            .amount-positive {
                color: #22c55e;
            }

            .amount-negative {
                color: #ef4444;
            }

            .transaction-info {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .info-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .info-row:last-child {
                border-bottom: none;
            }

            .info-label {
                font-weight: 500;
                color: rgba(255, 255, 255, 0.7);
            }

            .info-value {
                font-weight: 600;
                color: #ffffff;
            }

            .user-display {
                display: flex;
                flex-direction: column;
                gap: 0.1rem;
            }

            .user-display .user-name {
                font-weight: 600;
                color: #ffffff;
                font-size: 0.9rem;
            }

            .user-display .user-username {
                font-size: 0.75rem;
                color: rgba(255, 255, 255, 0.7);
                font-weight: 400;
            }

            .empty-state {
                grid-column: 1 / -1;
                text-align: center;
                padding: 4rem 2rem;
                color: rgba(255, 255, 255, 0.6);
            }

            .empty-state i {
                font-size: 4rem;
                margin-bottom: 1rem;
                opacity: 0.5;
            }

            .empty-state h3 {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
                color: rgba(255, 255, 255, 0.8);
            }

            .empty-state p {
                font-size: 1rem;
                margin: 0;
            }

            @media (max-width: 1200px) {
                .main-content {
                    margin-left: 0;
                }

                .epic-sidebar {
                    transform: translateX(-100%);
                }
            }

            @media (max-width: 768px) {
                .epic-header {
                    padding: 1.5rem 1rem;
                }

                .transactions-section {
                    padding: 1.5rem 1rem;
                }

                .stats-overview {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }

                .search-section {
                    flex-direction: column;
                    align-items: stretch;
                }

                .search-bar {
                    max-width: none;
                }

                .filter-buttons {
                    justify-content: center;
                    flex-wrap: wrap;
                }

                .transactions-grid {
                    grid-template-columns: 1fr;
                }

                .header-content {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .header-title h1 {
                    font-size: 2rem;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
