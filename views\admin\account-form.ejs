<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= isEdit ? 'Edit' : 'Add New' %> Gaming Account - Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        /* Epic Animated Background */
        .dashboard-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }
        
        .dashboard-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 128, 0.05) 0%, transparent 50%);
            animation: float 25s ease-in-out infinite;
        }
        
        .dashboard-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 212, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.02) 1px, transparent 1px);
            background-size: 60px 60px;
            animation: gridMove 40s linear infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }
        
        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(60px, 60px); }
        }
        
        /* Epic Sidebar */
        .epic-sidebar {
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(0, 212, 255, 0.2);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .epic-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, 
                rgba(0, 212, 255, 0.05) 0%, 
                rgba(255, 0, 128, 0.05) 50%, 
                rgba(0, 212, 255, 0.05) 100%);
            pointer-events: none;
        }
        
        /* Sidebar Brand */
        .sidebar-brand {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }
        
        .brand-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
        }
        
        .brand-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .brand-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 14px;
            opacity: 0;
            filter: blur(8px);
            transition: opacity 0.3s ease;
            animation: brandPulse 3s ease-in-out infinite;
        }
        
        @keyframes brandPulse {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.4; }
        }
        
        .brand-icon i {
            font-size: 1.5rem;
            color: white;
            z-index: 2;
        }
        
        .brand-text h4 {
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            font-size: 1.3rem;
        }
        
        .brand-text span {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        /* Sidebar Navigation */
        .sidebar-nav {
            padding: 1rem 0;
            position: relative;
            z-index: 2;
        }
        
        .nav-section {
            margin-bottom: 2rem;
        }
        
        .nav-section-title {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 1.5rem;
            margin-bottom: 1rem;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-radius: 0;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }
        
        /* Epic Header */
        .epic-header {
            background: rgba(10, 10, 15, 0.9);
            backdrop-filter: blur(25px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 2rem 3rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-title h1 {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }
        
        .header-title i {
            font-size: 2rem;
            color: #00d4ff;
        }
        
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .action-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            transform: translateY(-2px);
        }

        /* Epic Form Styles */
        .form-section {
            padding: 3rem;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            position: relative;
            overflow: hidden;
        }

        .form-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(255, 0, 128, 0.05));
            opacity: 0.5;
            pointer-events: none;
        }

        .form-header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
            z-index: 2;
        }

        .form-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
            position: relative;
        }

        .form-icon::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 23px;
            opacity: 0;
            filter: blur(10px);
            animation: iconPulse 3s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.6; }
        }

        .form-header h2 {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0 0 0.5rem;
        }

        .form-header p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
            margin: 0;
        }

        .epic-form {
            position: relative;
            z-index: 2;
        }

        .form-section-group {
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .form-section-group:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(0, 212, 255, 0.2);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.3rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 1.5rem;
        }

        .section-title i {
            font-size: 1.2rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .input-group {
            position: relative;
            transition: all 0.3s ease;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            z-index: 2;
            font-size: 1.1rem;
        }

        .epic-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 1rem 1rem 1rem 3rem;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
            resize: vertical;
        }

        .epic-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .epic-input:focus {
            outline: none;
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        }

        /* Select dropdown styling */
        select.epic-input {
            background: rgba(255, 255, 255, 0.08);
            color: #ffffff;
            cursor: pointer;
        }

        select.epic-input option {
            background: #1a1a2e;
            color: #ffffff;
            padding: 0.75rem;
            border: none;
        }

        select.epic-input option:hover {
            background: rgba(0, 212, 255, 0.2);
        }

        select.epic-input option:checked {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
        }

        /* Custom dropdown arrow */
        select.epic-input {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300d4ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 1rem center;
            background-size: 1rem;
            padding-right: 3rem;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #00d4ff;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .checkbox-group:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
        }

        .epic-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #00d4ff;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin: 0;
            cursor: pointer;
        }

        /* Epic Image Upload Styles */
        .epic-upload-area {
            position: relative;
            border: 2px dashed rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 3rem 2rem;
            text-align: center;
            background: rgba(0, 212, 255, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            overflow: hidden;
        }

        .epic-upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(255, 0, 128, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .epic-upload-area:hover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            transform: translateY(-2px);
        }

        .epic-upload-area:hover::before {
            opacity: 1;
        }

        .epic-upload-area.dragover {
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.15);
            transform: scale(1.02);
        }

        .upload-content {
            position: relative;
            z-index: 2;
        }

        .upload-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
            animation: uploadPulse 2s ease-in-out infinite;
        }

        @keyframes uploadPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .upload-content h4 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 0.5rem;
        }

        .upload-content p {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 0.5rem;
        }

        .upload-link {
            color: #00d4ff;
            font-weight: 600;
            text-decoration: underline;
        }

        .upload-content small {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.85rem;
        }

        .upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .image-preview-container {
            margin-top: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }

        .image-preview {
            position: relative;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            aspect-ratio: 1;
            transition: all 0.3s ease;
        }

        .image-preview:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
        }

        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .image-remove:hover {
            background: rgba(255, 0, 0, 1);
            transform: scale(1.1);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn-secondary,
        .btn-primary {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            text-decoration: none;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.8);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
            }

            .epic-sidebar {
                transform: translateX(-100%);
            }
        }

        @media (max-width: 768px) {
            .epic-header {
                padding: 1.5rem 1rem;
            }

            .form-section {
                padding: 1.5rem 1rem;
            }

            .form-card {
                padding: 2rem 1.5rem;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .form-actions {
                flex-direction: column;
            }

            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }

            .header-title h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-bg"></div>

    <!-- Epic Sidebar -->
    <div class="epic-sidebar">
        <div class="sidebar-brand">
            <a href="/" class="brand-logo">
                <div class="brand-icon">
                    <i class="bi bi-controller"></i>
                </div>
                <div class="brand-text">
                    <h4>Gaming Store</h4>
                    <span>Admin Panel</span>
                </div>
            </a>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="/admin/dashboard" class="nav-link">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <div class="nav-item">
                    <a href="/admin/users" class="nav-link">
                        <i class="bi bi-people"></i>
                        <span>Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/accounts" class="nav-link active">
                        <i class="bi bi-controller"></i>
                        <span>Gaming Accounts</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/transactions" class="nav-link">
                        <i class="bi bi-credit-card"></i>
                        <span>Transactions</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/purchases" class="nav-link">
                        <i class="bi bi-bag-check"></i>
                        <span>Purchases</span>
                    </a>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <div class="nav-item">
                    <a href="/admin/broadcast" class="nav-link">
                        <i class="bi bi-megaphone"></i>
                        <span>Broadcast</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/health" class="nav-link">
                        <i class="bi bi-heart-pulse"></i>
                        <span>System Health</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/settings" class="nav-link">
                        <i class="bi bi-gear"></i>
                        <span>Settings</span>
                    </a>
                </div>
                <div class="nav-item">
                    <form action="/admin/logout" method="POST" style="margin: 0;">
                        <button type="submit" class="nav-link" style="background: none; border: none; width: 100%; text-align: left;">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Epic Header -->
        <div class="epic-header">
            <div class="header-content">
                <div class="header-title">
                    <i class="bi bi-plus-circle"></i>
                    <h1><%= isEdit ? 'Edit' : 'Add New' %> Account</h1>
                </div>
                <div class="header-actions">
                    <a href="/admin/accounts" class="action-btn">
                        <i class="bi bi-arrow-left"></i>
                        <span>Back to Accounts</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Epic Form Section -->
        <div class="form-section">
            <div class="form-container">
                <div class="form-card">
                    <div class="form-header">
                        <div class="form-icon">
                            <i class="bi bi-controller"></i>
                        </div>
                        <h2><%= isEdit ? 'Edit' : 'Create' %> Gaming Account</h2>
                        <p><%= isEdit ? 'Update the gaming account details' : 'Add a new gaming account to your store inventory' %></p>
                    </div>

                    <form action="<%= isEdit ? `/admin/accounts/${account.id}` : '/admin/accounts' %>" method="POST" class="epic-form" id="accountForm" enctype="multipart/form-data">
                        <% if (isEdit) { %>
                        <input type="hidden" name="_method" value="PUT">
                        <% } %>

                        <!-- Basic Information Section -->
                        <div class="form-section-group">
                            <h3 class="section-title">
                                <i class="bi bi-info-circle"></i>
                                Basic Information
                            </h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="title" class="form-label">Account Title *</label>
                                    <div class="input-group">
                                        <i class="bi bi-tag input-icon"></i>
                                        <input type="text" class="form-control epic-input" id="title" name="title"
                                               placeholder="e.g., Valorant Immortal Account"
                                               value="<%= isEdit && account ? account.title : '' %>" required>
                                    </div>
                                </div>

                                <!-- Game Type field removed as requested -->
                            </div>

                            <div class="form-group">
                                <label for="description" class="form-label">Description</label>
                                <div class="input-group">
                                    <i class="bi bi-card-text input-icon"></i>
                                    <textarea class="form-control epic-input" id="description" name="description"
                                              rows="4" placeholder="Detailed description of the account, including rank, skins, achievements, etc."><%= isEdit && account ? account.description || '' : '' %></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Epic Image Upload Section -->
                        <div class="form-section-group">
                            <h3 class="section-title">
                                <i class="bi bi-images"></i>
                                Account Screenshots
                            </h3>

                            <div class="form-group">
                                <label for="images" class="form-label">Upload Account Images *</label>
                                <div class="epic-upload-area" id="uploadArea">
                                    <div class="upload-content">
                                        <div class="upload-icon">
                                            <i class="bi bi-cloud-upload"></i>
                                        </div>
                                        <h4>Drag & Drop Images Here</h4>
                                        <p>or <span class="upload-link">click to browse</span></p>
                                        <small>Support: JPG, PNG, GIF, WEBP (Max 5MB each, up to 10 images)</small>
                                    </div>
                                    <input type="file" id="images" name="images" multiple accept="image/*" class="upload-input">
                                </div>

                                <div class="image-preview-container" id="imagePreview">
                                    <!-- Image previews will be added here -->
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Section -->
                        <div class="form-section-group">
                            <h3 class="section-title">
                                <i class="bi bi-currency-dollar"></i>
                                Pricing
                            </h3>

                            <div class="form-group">
                                <label for="price" class="form-label">Price (USD) *</label>
                                <div class="input-group">
                                    <i class="bi bi-currency-dollar input-icon"></i>
                                    <input type="number" class="form-control epic-input" id="price" name="price"
                                           placeholder="0.00" step="0.01" min="0" required
                                           value="<%= isEdit && account ? account.price || '' : '' %>">
                                </div>
                                <small class="form-text">Set the price for this gaming account</small>
                            </div>
                        </div>

                        <!-- Account Credentials Section -->
                        <div class="form-section-group">
                            <h3 class="section-title">
                                <i class="bi bi-key"></i>
                                Account Credentials
                            </h3>

                            <div class="form-group">
                                <label for="account_credentials" class="form-label">Account Credentials *</label>
                                <div class="input-group">
                                    <i class="bi bi-clipboard input-icon"></i>
                                    <textarea class="form-control epic-input" id="account_credentials" name="account_credentials"
                                              rows="3" placeholder="Paste complete account credentials here (e.g., <EMAIL>:Zittu12345 | Level: 177 | Name: N/A | UID: ******** | Rank: N/A | Bind: N/A | Country: N/A | Date: N/A | Banned: N/A)" required><%= isEdit && account ? account.account_credentials || '' : '' %></textarea>
                                </div>
                                <small class="form-text">Paste the complete account credential string exactly as it should be delivered to the buyer</small>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="account_code" class="form-label">Account Code</label>
                                    <div class="input-group">
                                        <i class="bi bi-key input-icon"></i>
                                        <input type="text" class="form-control epic-input" id="account_code" name="account_code"
                                               placeholder="Auto-generated if empty"
                                               value="<%= isEdit && account ? account.account_code || '' : '' %>">
                                        <button type="button" class="btn btn-outline-secondary" onclick="generateAccountCode()">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <small class="form-text">Leave empty to auto-generate a unique code</small>
                                </div>

                                <div class="form-group">
                                    <label for="additional_info" class="form-label">Additional Information</label>
                                    <div class="input-group">
                                        <i class="bi bi-info-square input-icon"></i>
                                        <textarea class="form-control epic-input" id="additional_info" name="additional_info"
                                                  rows="3" placeholder="Any additional account details, security info, or special notes..."><%= isEdit && account ? account.additional_info || '' : '' %></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information Section -->
                        <div class="form-section-group">
                            <h3 class="section-title">
                                <i class="bi bi-info-circle"></i>
                                Account Information
                            </h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="account_status" class="form-label">Account Status</label>
                                    <div class="input-group">
                                        <i class="bi bi-shield-check input-icon"></i>
                                        <input type="text" class="form-control epic-input" id="account_status" name="account_status"
                                               placeholder="e.g., Good, Excellent, Fair"
                                               value="<%= isEdit && account ? account.account_status || '' : '' %>">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="verify_code" class="form-label">Verify Code</label>
                                    <div class="input-group">
                                        <i class="bi bi-patch-check input-icon"></i>
                                        <input type="text" class="form-control epic-input" id="verify_code" name="verify_code"
                                               placeholder="e.g., Bug Code, Clean, Verified"
                                               value="<%= isEdit && account ? account.verify_code || '' : '' %>">
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="inactive_status" class="form-label">Inactive Status</label>
                                    <div class="input-group">
                                        <i class="bi bi-clock input-icon"></i>
                                        <input type="text" class="form-control epic-input" id="inactive_status" name="inactive_status"
                                               placeholder="e.g., 4Days+, 1Week+, Active"
                                               value="<%= isEdit && account ? account.inactive_status || '' : '' %>">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="collector_status" class="form-label">Collector Status</label>
                                    <div class="input-group">
                                        <i class="bi bi-trophy input-icon"></i>
                                        <select class="form-control epic-input" id="collector_status" name="collector_status">
                                            <option value="">Select Collector Status</option>
                                            <option value="Seasoned Collector" <%= isEdit && account && account.collector_status === 'Seasoned Collector' ? 'selected' : '' %>>Seasoned Collector</option>
                                            <option value="Expert Collector" <%= isEdit && account && account.collector_status === 'Expert Collector' ? 'selected' : '' %>>Expert Collector</option>
                                            <option value="Renowned Collector" <%= isEdit && account && account.collector_status === 'Renowned Collector' ? 'selected' : '' %>>Renowned Collector</option>
                                            <option value="Exalted Collector" <%= isEdit && account && account.collector_status === 'Exalted Collector' ? 'selected' : '' %>>Exalted Collector</option>
                                            <option value="Mega Collector" <%= isEdit && account && account.collector_status === 'Mega Collector' ? 'selected' : '' %>>Mega Collector</option>
                                            <option value="World Collector" <%= isEdit && account && account.collector_status === 'World Collector' ? 'selected' : '' %>>World Collector</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="device_info" class="form-label">Device Information</label>
                                    <div class="input-group">
                                        <i class="bi bi-phone input-icon"></i>
                                        <input type="text" class="form-control epic-input" id="device_info" name="device_info"
                                               placeholder="e.g., IDK, Mobile, PC, Unknown"
                                               value="<%= isEdit && account ? account.device_info || '' : '' %>">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <!-- Empty space for alignment -->
                                </div>
                            </div>
                        </div>

                        <!-- Status Section -->
                        <div class="form-section-group">
                            <h3 class="section-title">
                                <i class="bi bi-gear"></i>
                                Account Status
                            </h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <div class="checkbox-group">
                                        <input type="checkbox" class="epic-checkbox" id="is_available" name="is_available"
                                               <%= isEdit && account ? (account.is_available ? 'checked' : '') : 'checked' %>>
                                        <label for="is_available" class="checkbox-label">
                                            <i class="bi bi-check-circle"></i>
                                            Available for Sale
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-group">
                                        <input type="checkbox" class="epic-checkbox" id="is_featured" name="is_featured"
                                               <%= isEdit && account && account.is_featured ? 'checked' : '' %>>
                                        <label for="is_featured" class="checkbox-label">
                                            <i class="bi bi-star"></i>
                                            Featured Account
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="window.history.back()">
                                <i class="bi bi-x-circle"></i>
                                <span>Cancel</span>
                            </button>
                            <button type="submit" class="btn-primary">
                                <i class="bi bi-<%= isEdit ? 'check' : 'plus' %>-circle"></i>
                                <span><%= isEdit ? 'Update' : 'Create' %> Account</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Epic Form JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Animate form sections on load
            const formSections = document.querySelectorAll('.form-section-group');
            formSections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // Real-time validation
            const inputs = document.querySelectorAll('.epic-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });

        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        function generateAccountCode() {
            // Generate a random 6-digit account code
            const code = Math.floor(100000 + Math.random() * 900000);
            document.getElementById('account_code').value = code;
        }

        // Epic Image Upload Functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('images');
        const imagePreview = document.getElementById('imagePreview');
        let selectedFiles = [];

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        function handleFiles(files) {
            // Filter only image files
            const imageFiles = files.filter(file => file.type.startsWith('image/'));

            // Check file size (5MB limit)
            const validFiles = imageFiles.filter(file => {
                if (file.size > 5 * 1024 * 1024) {
                    alert(`File ${file.name} is too large. Maximum size is 5MB.`);
                    return false;
                }
                return true;
            });

            // Check total file count (10 max)
            if (selectedFiles.length + validFiles.length > 10) {
                alert('Maximum 10 images allowed.');
                return;
            }

            // Add files to selection
            validFiles.forEach(file => {
                selectedFiles.push(file);
                createImagePreview(file);
            });

            updateFileInput();
        }

        function createImagePreview(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'image-preview';
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <button type="button" class="image-remove" onclick="removeImage(this, '${file.name}')">
                        <i class="bi bi-x"></i>
                    </button>
                `;

                // Add animation
                previewDiv.style.opacity = '0';
                previewDiv.style.transform = 'scale(0.8)';
                imagePreview.appendChild(previewDiv);

                setTimeout(() => {
                    previewDiv.style.transition = 'all 0.3s ease';
                    previewDiv.style.opacity = '1';
                    previewDiv.style.transform = 'scale(1)';
                }, 100);
            };
            reader.readAsDataURL(file);
        }

        function removeImage(button, fileName) {
            // Remove from selectedFiles array
            selectedFiles = selectedFiles.filter(file => file.name !== fileName);

            // Remove preview with animation
            const previewDiv = button.parentElement;
            previewDiv.style.transition = 'all 0.3s ease';
            previewDiv.style.opacity = '0';
            previewDiv.style.transform = 'scale(0.8)';

            setTimeout(() => {
                previewDiv.remove();
            }, 300);

            updateFileInput();
        }

        function updateFileInput() {
            // Create new FileList from selectedFiles
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;
        }
    </script>
</body>
</html>
