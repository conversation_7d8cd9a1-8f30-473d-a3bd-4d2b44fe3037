<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users Management - Gaming Accounts Store</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0f;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        /* Epic Animated Background */
        .dashboard-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }
        
        .dashboard-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 255, 128, 0.05) 0%, transparent 50%);
            animation: float 25s ease-in-out infinite;
        }
        
        .dashboard-bg::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 212, 255, 0.02) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.02) 1px, transparent 1px);
            background-size: 60px 60px;
            animation: gridMove 40s linear infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(-10px) rotate(-1deg); }
        }
        
        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(60px, 60px); }
        }
        
        /* Epic Sidebar */
        .epic-sidebar {
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(0, 212, 255, 0.2);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }
        
        .epic-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, 
                rgba(0, 212, 255, 0.05) 0%, 
                rgba(255, 0, 128, 0.05) 50%, 
                rgba(0, 212, 255, 0.05) 100%);
            pointer-events: none;
        }
        
        /* Sidebar Brand */
        .sidebar-brand {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }
        
        .brand-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
        }
        
        .brand-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .brand-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 14px;
            opacity: 0;
            filter: blur(8px);
            transition: opacity 0.3s ease;
            animation: brandPulse 3s ease-in-out infinite;
        }
        
        @keyframes brandPulse {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.4; }
        }
        
        .brand-icon i {
            font-size: 1.5rem;
            color: white;
            z-index: 2;
        }
        
        .brand-text h4 {
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            font-size: 1.3rem;
        }
        
        .brand-text span {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        /* Sidebar Navigation */
        .sidebar-nav {
            padding: 1rem 0;
            position: relative;
            z-index: 2;
        }
        
        .nav-section {
            margin-bottom: 2rem;
        }
        
        .nav-section-title {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 1.5rem;
            margin-bottom: 1rem;
        }
        
        .nav-item {
            margin-bottom: 0.25rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-radius: 0;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
        }
        
        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            position: relative;
            z-index: 1;
        }
        
        /* Epic Header */
        .epic-header {
            background: rgba(10, 10, 15, 0.9);
            backdrop-filter: blur(25px);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 2rem 3rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .header-title h1 {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }
        
        .header-title i {
            font-size: 2rem;
            color: #00d4ff;
        }
        
        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .action-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            transform: translateY(-2px);
        }

        /* Users Section */
        .users-section {
            padding: 3rem;
        }

        /* Stats Overview */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(0, 212, 255, 0.3);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-info h3 {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #00d4ff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .stat-info p {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-weight: 500;
        }

        /* Search Section */
        .search-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-bar {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .search-bar i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            z-index: 2;
        }

        .search-bar input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 0.75rem 1rem 0.75rem 3rem;
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-bar input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .search-bar input:focus {
            outline: none;
            border-color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .filter-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.7);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            color: #00d4ff;
        }

        /* Users Grid */
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
        }

        .user-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .user-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.05), rgba(255, 0, 128, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .user-card:hover {
            transform: translateY(-10px);
            border-color: rgba(0, 212, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .user-card:hover::before {
            opacity: 1;
        }

        .user-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .user-avatar {
            font-size: 3rem;
            color: #00d4ff;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            overflow: hidden;
            background: rgba(0, 212, 255, 0.1);
            border: 2px solid rgba(0, 212, 255, 0.3);
        }

        .user-avatar .profile-picture {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
            /* Let browser handle image quality naturally */
            display: block;
        }

        .user-avatar .profile-picture:hover {
            transform: scale(1.05);
        }

        .user-avatar i {
            font-size: 4rem;
        }

        /* Developer Card Styles */
        .developer-card {
            position: relative;
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.15) 0%,
                rgba(255, 140, 0, 0.15) 25%,
                rgba(255, 69, 0, 0.15) 50%,
                rgba(255, 20, 147, 0.15) 75%,
                rgba(138, 43, 226, 0.15) 100%);
            border: 2px solid transparent;
            background-clip: padding-box;
            overflow: hidden;
        }

        .developer-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg,
                #FFD700, #FF8C00, #FF4500, #FF1493, #8A2BE2,
                #4169E1, #00CED1, #00FF7F, #FFD700);
            background-size: 400% 400%;
            border-radius: 22px;
            z-index: -1;
            animation: developerGlow 3s ease-in-out infinite;
        }

        .developer-card::after {
            content: '👑';
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 2rem;
            z-index: 10;
            animation: crownFloat 2s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
        }

        @keyframes developerGlow {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        @keyframes crownFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-5px) rotate(5deg);
            }
        }

        .developer-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(255, 215, 0, 0.3),
                inset 0 0 20px rgba(255, 215, 0, 0.1);
        }

        .developer-card .user-avatar {
            border: 3px solid #FFD700 !important;
            border-radius: 50% !important;
            width: 120px !important;
            height: 120px !important;
            min-width: 120px !important;
            min-height: 120px !important;
            max-width: 120px !important;
            max-height: 120px !important;
            box-shadow:
                0 0 20px rgba(255, 215, 0, 0.5),
                inset 0 0 10px rgba(255, 215, 0, 0.2);
            animation: avatarPulse 2s ease-in-out infinite;
            overflow: hidden !important;
            aspect-ratio: 1 / 1 !important;
        }

        .developer-card .user-avatar .profile-picture {
            border-radius: 50% !important;
            width: 120px !important;
            height: 120px !important;
            object-fit: cover !important;
        }

        @keyframes avatarPulse {
            0%, 100% {
                box-shadow:
                    0 0 20px rgba(255, 215, 0, 0.5),
                    inset 0 0 10px rgba(255, 215, 0, 0.2);
            }
            50% {
                box-shadow:
                    0 0 30px rgba(255, 215, 0, 0.8),
                    inset 0 0 15px rgba(255, 215, 0, 0.4);
            }
        }

        /* Developer Badge Styles */
        .developer-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: #000;
            padding: 0.2rem 0.6rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: 0.5rem;
            box-shadow:
                0 4px 15px rgba(255, 215, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            animation: badgeShine 2s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .developer-badge::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent);
            animation: badgeGlint 3s ease-in-out infinite;
        }

        @keyframes badgeShine {
            0%, 100% {
                box-shadow:
                    0 4px 15px rgba(255, 215, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% {
                box-shadow:
                    0 6px 25px rgba(255, 215, 0, 0.6),
                    inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }
        }

        @keyframes badgeGlint {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            50% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
            100% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        .developer-badge i {
            font-size: 0.8rem;
            animation: codeIcon 1.5s ease-in-out infinite;
        }

        @keyframes codeIcon {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* Special effects for developer card details */
        .developer-card .detail-value.balance {
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .user-status-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .user-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .user-status.active {
            color: #22c55e;
        }

        .user-status.inactive {
            color: #ef4444;
        }

        .user-status i {
            font-size: 0.6rem;
        }

        .user-last-seen {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: #6c757d;
            font-weight: 400;
        }

        .user-last-seen i {
            font-size: 0.65rem;
        }

        .user-info {
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .user-info h4 {
            font-size: 1.3rem;
            font-weight: 700;
            color: #ffffff;
            margin: 0 0 0.5rem;
        }

        .username {
            color: #00d4ff;
            font-weight: 500;
            margin: 0 0 0.25rem;
        }

        .telegram-id {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
            margin: 0;
        }

        .user-username {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin: 0 0 0.5rem;
        }

        .user-details {
            margin-bottom: 1.5rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .detail-value {
            color: #ffffff;
            font-weight: 500;
        }

        .detail-value.balance {
            color: #00d4ff;
            font-weight: 700;
        }

        .user-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .stat-item {
            text-align: center;
        }

        .stat-item .label {
            display: block;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .stat-item .value {
            display: block;
            color: #ffffff;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
            position: relative;
            z-index: 2;
        }

        .action-btn-sm {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-btn-sm:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
            color: #00d4ff;
            transform: translateY(-2px);
        }

        .action-btn-sm.danger:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: #ef4444;
            color: #ef4444;
        }

        /* Empty State */
        .empty-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 4rem 2rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-state p {
            font-size: 1rem;
            margin: 0;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: rgba(10, 10, 15, 0.95);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 50px rgba(0, 212, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-header h3 {
            color: #00d4ff;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 2rem;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .modal-close:hover {
            color: #ff0080;
        }

        .modal-body {
            padding: 2rem;
        }

        .user-detail-grid {
            display: grid;
            gap: 1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-item label {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .detail-item span {
            color: #ffffff;
            font-weight: 600;
        }

        .balance-amount {
            color: #00d4ff !important;
            font-size: 1.1rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.active {
            background: rgba(0, 255, 128, 0.2);
            color: #00ff80;
        }

        .status-badge.inactive {
            background: rgba(255, 128, 0, 0.2);
            color: #ff8000;
        }

        .modal-footer {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding: 1.5rem 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #ffffff;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        /* Developer Modal Styling */
        .developer-modal .modal-content {
            border: 2px solid #FFD700;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.5),
                0 0 30px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 215, 0, 0.2);
        }

        .developer-header {
            background: linear-gradient(135deg, #FFD700, #FFA500) !important;
            color: #000 !important;
            font-weight: bold;
        }

        .developer-header h3 {
            color: #000 !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .developer-banner {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            animation: developerGlow 3s ease-in-out infinite;
        }

        @keyframes developerGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
            50% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.5); }
        }

        .developer-icon {
            font-size: 3rem;
            color: #FFD700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .developer-info h4 {
            color: #FFD700;
            margin: 0 0 0.5rem 0;
            font-weight: bold;
            text-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
        }

        .developer-info p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        }

        .developer-role {
            color: #FFD700 !important;
            font-weight: bold;
        }

        .developer-permissions {
            color: #00ff88 !important;
            font-weight: bold;
        }

        .developer-status {
            background: linear-gradient(135deg, #FFD700, #FFA500) !important;
            color: #000 !important;
            font-weight: bold;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
        }

        .action-btn-sm.protected {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            border: 1px solid #FFD700;
            cursor: not-allowed;
            opacity: 0.8;
        }

        .action-btn-sm.protected:hover {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            transform: none;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
            }

            .epic-sidebar {
                transform: translateX(-100%);
            }
        }

        @media (max-width: 768px) {
            .epic-header {
                padding: 1.5rem 1rem;
            }

            .users-section {
                padding: 1.5rem 1rem;
            }

            .stats-overview {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .search-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-bar {
                max-width: none;
            }

            .filter-buttons {
                justify-content: center;
                flex-wrap: wrap;
            }

            .users-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }

            .header-title h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-bg"></div>
    
    <!-- Epic Sidebar -->
    <div class="epic-sidebar">
        <div class="sidebar-brand">
            <a href="/" class="brand-logo">
                <div class="brand-icon">
                    <i class="bi bi-controller"></i>
                </div>
                <div class="brand-text">
                    <h4>Gaming Store</h4>
                    <span>Admin Panel</span>
                </div>
            </a>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">Main</div>
                <div class="nav-item">
                    <a href="/admin/dashboard" class="nav-link">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">Management</div>
                <div class="nav-item">
                    <a href="/admin/users" class="nav-link active">
                        <i class="bi bi-people"></i>
                        <span>Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/accounts" class="nav-link">
                        <i class="bi bi-controller"></i>
                        <span>Gaming Accounts</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/transactions" class="nav-link">
                        <i class="bi bi-credit-card"></i>
                        <span>Transactions</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/purchases" class="nav-link">
                        <i class="bi bi-bag-check"></i>
                        <span>Purchases</span>
                    </a>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">System</div>
                <div class="nav-item">
                    <a href="/admin/admin-users" class="nav-link">
                        <i class="bi bi-shield-lock"></i>
                        <span>Admin Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/broadcast" class="nav-link">
                        <i class="bi bi-megaphone"></i>
                        <span>Broadcast</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/health" class="nav-link">
                        <i class="bi bi-heart-pulse"></i>
                        <span>System Health</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/admin/settings" class="nav-link">
                        <i class="bi bi-gear"></i>
                        <span>Settings</span>
                    </a>
                </div>
                <div class="nav-item">
                    <form action="/admin/logout" method="POST" style="margin: 0;">
                        <button type="submit" class="nav-link" style="background: none; border: none; width: 100%; text-align: left;">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>Logout</span>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Epic Header -->
        <div class="epic-header">
            <div class="header-content">
                <div class="header-title">
                    <i class="bi bi-people"></i>
                    <h1>Users Management</h1>
                </div>
                <div class="header-actions">
                    <button class="action-btn primary" onclick="showAddUserModal()">
                        <i class="bi bi-person-plus"></i>
                        <span>Add User</span>
                    </button>
                    <a href="#" class="action-btn" onclick="refreshUsers()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>Refresh</span>
                    </a>
                    <a href="#" class="action-btn" onclick="exportUsers()">
                        <i class="bi bi-download"></i>
                        <span>Export</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Users Content -->
        <div class="users-section">
            <!-- Stats Overview -->
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= userStats.total || 0 %></h3>
                        <p>Total Users</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= userStats.active || 0 %></h3>
                        <p>Active Users</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-calendar-plus"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= userStats.newThisWeek || 0 %></h3>
                        <p>New This Week</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="stat-info">
                        <h3><%= formatCurrency(userStats.totalBalance || 0) %></h3>
                        <p>Total Balance</p>
                    </div>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="search-section">
                <div class="search-bar">
                    <i class="bi bi-search"></i>
                    <input type="text" id="userSearch" placeholder="Search users by name, username, or Telegram ID...">
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Users</button>
                    <button class="filter-btn" data-filter="active">Active</button>
                    <button class="filter-btn" data-filter="inactive">Inactive</button>
                    <button class="filter-btn" data-filter="banned">Banned</button>
                    <button class="filter-btn" data-filter="recent">Recent</button>
                </div>
            </div>

            <!-- Users Grid -->
            <div class="users-grid" id="usersGrid">
                <% if (users && users.length > 0) { %>
                    <% users.forEach(user => { %>
                    <div class="user-card <%= user.telegram_id == ********** ? 'developer-card' : '' %> <%= user.is_banned ? 'banned-card' : '' %>" data-status="<%= user.is_active ? 'active' : 'inactive' %>" data-banned="<%= user.is_banned ? 'true' : 'false' %>">
                        <div class="user-header">
                            <div class="user-avatar">
                                <% if (user.profile_picture_url) { %>
                                    <img src="<%= user.profile_picture_url %>" alt="<%= user.first_name %>'s profile" class="profile-picture">
                                <% } else { %>
                                    <i class="bi bi-person-circle"></i>
                                <% } %>
                            </div>
                            <div class="user-info">
                                <h4>
                                    <%= user.first_name %> <%= user.last_name || '' %>
                                    <% if (user.telegram_id == **********) { %>
                                        <span class="developer-badge">
                                            <i class="bi bi-code-slash"></i>
                                            <span>DEVELOPER</span>
                                        </span>
                                    <% } %>
                                    <% if (user.is_banned) { %>
                                        <span class="banned-badge">
                                            <i class="bi bi-ban"></i>
                                            <span>BANNED</span>
                                        </span>
                                    <% } %>
                                </h4>
                                <p class="user-username">@<%= user.username || 'No username' %></p>
                                <div class="user-status-container">
                                    <span class="user-status <%= user.is_active ? 'active' : 'inactive' %>">
                                        <i class="bi bi-circle-fill"></i>
                                        Account: <%= user.is_active ? 'Active' : 'Inactive' %>
                                    </span>
                                    <span class="user-last-seen">
                                        <i class="bi bi-clock"></i>
                                        Last seen: <%= formatLastActive(user.last_active) %>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="user-details">
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="bi bi-telegram"></i>
                                    Telegram ID
                                </span>
                                <span class="detail-value"><%= user.telegram_id %></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="bi bi-wallet2"></i>
                                    Balance
                                </span>
                                <span class="detail-value balance"><%= formatCurrency(user.balance) %></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="bi bi-calendar-plus"></i>
                                    Joined
                                </span>
                                <span class="detail-value"><%= formatDate(user.registration_date) %></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="bi bi-activity"></i>
                                    Last Active
                                </span>
                                <span class="detail-value"><%= formatLastActive(user.last_active) %></span>
                            </div>
                        </div>

                        <div class="user-actions">
                            <button class="action-btn-sm" onclick="viewUserDetails(<%= user.telegram_id %>)" title="View Details">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="action-btn-sm" onclick="viewUserTransactions(<%= user.telegram_id %>)" title="View Transactions">
                                <i class="bi bi-credit-card"></i>
                            </button>
                            <button class="action-btn-sm" onclick="adjustBalance(<%= user.telegram_id %>)" title="Adjust Balance">
                                <i class="bi bi-wallet2"></i>
                            </button>
                            <% if (user.telegram_id != **********) { %>
                                <button class="action-btn-sm" onclick="toggleUserStatus(<%= user.telegram_id %>, '<%= user.is_active ? 'false' : 'true' %>')" title="<%= user.is_active ? 'Deactivate' : 'Activate' %> User">
                                    <i class="bi bi-<%= user.is_active ? 'person-x' : 'person-check' %>"></i>
                                </button>
                                <% if (user.is_banned) { %>
                                    <button class="action-btn-sm warning" onclick="unbanUser(<%= user.telegram_id %>)" title="Unban User">
                                        <i class="bi bi-person-check"></i>
                                    </button>
                                <% } else { %>
                                    <button class="action-btn-sm danger" onclick="banUser(<%= user.telegram_id %>)" title="Ban User">
                                        <i class="bi bi-ban"></i>
                                    </button>
                                <% } %>
                                <button class="action-btn-sm danger" onclick="deleteUser(<%= user.telegram_id %>, '<%= user.first_name %> <%= user.last_name || '' %>')" title="Delete User">
                                    <i class="bi bi-trash"></i>
                                </button>
                            <% } else { %>
                                <button class="action-btn-sm protected" title="Developer account is protected" disabled>
                                    <i class="bi bi-shield-lock"></i>
                                </button>
                            <% } %>
                        </div>
                    </div>
                    <% }); %>
                <% } else { %>
                    <div class="empty-state">
                        <i class="bi bi-people"></i>
                        <h3>No Users Found</h3>
                        <p>No users have registered yet. Users will appear here when they start using the Telegram bot.</p>
                    </div>
                <% } %>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Epic Users JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Animate user cards on load
            const userCards = document.querySelectorAll('.user-card');
            userCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });

            // Animate stat cards
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateX(-30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateX(0)';
                }, index * 100);
            });

            // Search functionality
            const searchInput = document.getElementById('userSearch');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                filterUsers(searchTerm);
            });

            // Filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            filterButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    const filter = this.dataset.filter;
                    filterUsersByStatus(filter);
                });
            });
        });

        // Global functions for onclick handlers
        async function viewUserDetails(telegramId) {
            console.log('viewUserDetails called with telegramId:', telegramId);
            showNotification(`Fetching details for user ${telegramId}...`, 'info');

            try {
                const response = await fetch(`/admin/api/users/${telegramId}`);
                console.log('API response status:', response.status);
                const user = await response.json();
                console.log('User data:', user);

                if (response.ok) {
                    // Create and show user details modal
                    showUserDetailsModal(user);
                } else {
                    showNotification(user.error || 'Failed to fetch user details', 'error');
                }
            } catch (error) {
                console.error('Error fetching user details:', error);
                showNotification('Error fetching user details', 'error');
            }
        }

        function viewUserTransactions(telegramId) {
            alert('viewUserTransactions called with ID: ' + telegramId);
            window.location.href = `/admin/transactions?user=${telegramId}`;
        }

        async function adjustBalance(telegramId) {
            console.log('adjustBalance called with telegramId:', telegramId);
            const amount = prompt('Enter amount to add/subtract (use negative for subtract):');
            if (amount && !isNaN(amount)) {
                console.log('Adjusting balance by:', amount);
                try {
                    const response = await fetch(`/admin/api/users/${telegramId}/adjust-balance`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ amount: parseFloat(amount) })
                    });

                    const result = await response.json();
                    console.log('Adjust balance result:', result);

                    if (response.ok) {
                        showNotification(result.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(result.error || 'Failed to adjust balance', 'error');
                    }
                } catch (error) {
                    console.error('Error adjusting balance:', error);
                    showNotification('Error adjusting balance', 'error');
                }
            }
        }

        async function toggleUserStatus(telegramId, newStatus) {
            console.log('toggleUserStatus called:', { telegramId, newStatus, type: typeof newStatus });

            // Prevent deactivating the developer account
            if (telegramId == ********** && newStatus === 'false') {
                showNotification('❌ Cannot deactivate the developer account! This account is protected.', 'error');
                return;
            }

            const action = newStatus === 'true' ? 'activate' : 'deactivate';
            console.log('Action:', action);

            if (confirm(`Are you sure you want to ${action} this user?`)) {
                try {
                    const statusBoolean = newStatus === 'true';
                    console.log('Sending status:', statusBoolean);

                    const response = await fetch(`/admin/api/users/${telegramId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ status: statusBoolean })
                    });

                    console.log('Response status:', response.status);
                    const result = await response.json();
                    console.log('Response data:', result);

                    if (response.ok) {
                        showNotification(result.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(result.error || 'Failed to toggle user status', 'error');
                    }
                } catch (error) {
                    console.error('Error toggling user status:', error);
                    showNotification('Error toggling user status', 'error');
                }
            }
        }

        function filterUsers(searchTerm) {
            const userCards = document.querySelectorAll('.user-card');
            userCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function filterUsersByStatus(filter) {
            const userCards = document.querySelectorAll('.user-card');
            userCards.forEach(card => {
                const status = card.dataset.status;

                if (filter === 'all') {
                    card.style.display = 'block';
                } else if (filter === 'active' && status === 'active') {
                    card.style.display = 'block';
                } else if (filter === 'inactive' && status === 'inactive') {
                    card.style.display = 'block';
                } else if (filter === 'recent') {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }

                if (card.style.display === 'block') {
                    card.style.animation = 'fadeIn 0.3s ease';
                }
            });
        }



        function viewUser(userId) {
            viewUserDetails(userId);
        }

        function showUserDetailsModal(user) {
            const isDeveloper = user.telegram_id == **********;
            const modalClass = isDeveloper ? 'developer-modal' : '';
            const headerClass = isDeveloper ? 'developer-header' : '';

            // Create modal HTML
            const modalHTML = `
                <div class="modal-overlay ${modalClass}" id="userDetailsModal" onclick="closeUserDetailsModal()">
                    <div class="modal-content" onclick="event.stopPropagation()">
                        <div class="modal-header ${headerClass}">
                            <h3>
                                ${isDeveloper ? '<i class="bi bi-crown-fill"></i> ' : ''}
                                User Details
                                ${isDeveloper ? ' <i class="bi bi-crown-fill"></i>' : ''}
                            </h3>
                            <button class="modal-close" onclick="closeUserDetailsModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            ${isDeveloper ? `
                                <div class="developer-banner">
                                    <div class="developer-icon">
                                        <i class="bi bi-code-slash"></i>
                                    </div>
                                    <div class="developer-info">
                                        <h4>🚀 DEVELOPER ACCOUNT</h4>
                                        <p>This is the website developer's account with special privileges</p>
                                    </div>
                                </div>
                            ` : ''}
                            <div class="user-detail-grid">
                                <div class="detail-item">
                                    <label>Full Name:</label>
                                    <span>${user.first_name} ${user.last_name || ''}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Username:</label>
                                    <span>@${user.username || 'No username'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Telegram ID:</label>
                                    <span>${user.telegram_id}</span>
                                </div>
                                ${isDeveloper ? `
                                    <div class="detail-item">
                                        <label>Role:</label>
                                        <span class="developer-role">
                                            <i class="bi bi-gear-fill"></i>
                                            Website Developer & Administrator
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <label>Permissions:</label>
                                        <span class="developer-permissions">
                                            <i class="bi bi-shield-check"></i>
                                            Full System Access
                                        </span>
                                    </div>
                                ` : ''}
                                <div class="detail-item">
                                    <label>Balance:</label>
                                    <span class="balance-amount">$${user.balance.toFixed(2)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Registration Date:</label>
                                    <span>${new Date(user.registration_date).toLocaleDateString()}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Last Bot Interaction:</label>
                                    <span>${user.last_active ? new Date(user.last_active).toLocaleDateString() : 'Never'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Status:</label>
                                    <span class="status-badge ${user.is_active ? 'active' : 'inactive'} ${isDeveloper ? 'developer-status' : ''}">
                                        ${isDeveloper ? '<i class="bi bi-shield-lock-fill"></i> ' : ''}
                                        ${user.is_active ? 'Active' : 'Inactive'}
                                        ${isDeveloper ? ' (Protected)' : ''}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="closeUserDetailsModal()">Close</button>
                            <button class="btn btn-primary" onclick="viewUserTransactions(${user.telegram_id})">View Transactions</button>
                            ${isDeveloper ? `
                                <button class="btn btn-warning" onclick="showDeveloperInfo()">
                                    <i class="bi bi-info-circle"></i>
                                    Developer Info
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeUserDetailsModal() {
            const modal = document.getElementById('userDetailsModal');
            if (modal) {
                modal.remove();
            }
        }

        function showDeveloperInfo() {
            const infoHTML = `
                <div class="modal-overlay" id="developerInfoModal" onclick="closeDeveloperInfoModal()">
                    <div class="modal-content developer-modal" onclick="event.stopPropagation()">
                        <div class="modal-header developer-header">
                            <h3>
                                <i class="bi bi-code-slash"></i>
                                Developer Information
                                <i class="bi bi-code-slash"></i>
                            </h3>
                            <button class="modal-close" onclick="closeDeveloperInfoModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="developer-banner">
                                <div class="developer-icon">
                                    <i class="bi bi-laptop"></i>
                                </div>
                                <div class="developer-info">
                                    <h4>🎮 Gaming Accounts Store</h4>
                                    <p>Telegram Bot & Admin Panel System</p>
                                </div>
                            </div>
                            <div class="user-detail-grid">
                                <div class="detail-item">
                                    <label>System Version:</label>
                                    <span>v2.0.0 - Advanced Edition</span>
                                </div>
                                <div class="detail-item">
                                    <label>Technologies:</label>
                                    <span>Node.js, Express, SQLite, Telegram Bot API</span>
                                </div>
                                <div class="detail-item">
                                    <label>Features:</label>
                                    <span>KHQR Payments, Account Management, User Analytics</span>
                                </div>
                                <div class="detail-item">
                                    <label>Security Level:</label>
                                    <span class="developer-permissions">
                                        <i class="bi bi-shield-check"></i>
                                        Maximum Protection
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <label>Account Protection:</label>
                                    <span class="developer-role">
                                        <i class="bi bi-lock-fill"></i>
                                        Cannot be deactivated or modified
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <label>Admin Panel:</label>
                                    <span>Full access to all system functions</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="closeDeveloperInfoModal()">Close</button>
                            <button class="btn btn-warning" onclick="window.open('https://github.com', '_blank')">
                                <i class="bi bi-github"></i>
                                View Source
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', infoHTML);
        }

        function closeDeveloperInfoModal() {
            const modal = document.getElementById('developerInfoModal');
            if (modal) {
                modal.remove();
            }
        }

        function editUser(userId) {
            showNotification(`Opening edit form for user ${userId}`, 'info');
        }

        function refreshUsers() {
            const refreshBtn = document.querySelector('.action-btn');
            const originalText = refreshBtn.innerHTML;

            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i><span>Refreshing...</span>';
            refreshBtn.style.pointerEvents = 'none';

            showNotification('Refreshing users list...', 'info');

            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
                refreshBtn.style.pointerEvents = 'auto';
                showNotification('Users list refreshed!', 'success');
            }, 1500);
        }

        function exportUsers() {
            showNotification('Preparing users export...', 'info');
            setTimeout(() => {
                showNotification('Export completed successfully!', 'success');
            }, 2000);
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 2rem;
                right: 2rem;
                background: ${type === 'success' ? 'rgba(34, 197, 94, 0.9)' : type === 'error' ? 'rgba(239, 68, 68, 0.9)' : 'rgba(59, 130, 246, 0.9)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                z-index: 10000;
                backdrop-filter: blur(10px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // User Management Functions
        let currentBanUserId = null;
        let currentDeleteUserId = null;

        // Show Add User Modal
        function showAddUserModal() {
            document.getElementById('addUserModal').style.display = 'flex';
            document.getElementById('addUserForm').reset();
        }

        // Close Add User Modal
        function closeAddUserModal() {
            document.getElementById('addUserModal').style.display = 'none';
        }

        // Submit Add User Form
        async function submitAddUser() {
            const form = document.getElementById('addUserForm');
            const formData = new FormData(form);
            const userData = Object.fromEntries(formData);

            try {
                const response = await fetch('/admin/api/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification('User created successfully!', 'success');
                    closeAddUserModal();
                    // Refresh the page to show the new user
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(result.error || 'Failed to create user', 'error');
                }
            } catch (error) {
                console.error('Error creating user:', error);
                showNotification('Error creating user', 'error');
            }
        }

        // Show Ban User Modal
        function banUser(telegramId) {
            currentBanUserId = telegramId;

            // Find user info
            const userCard = document.querySelector(`[data-status][data-banned] .user-info h4`);
            const userName = userCard ? userCard.textContent.trim().split('\n')[0] : `User ${telegramId}`;

            document.getElementById('banUserInfo').innerHTML = `
                <strong>User:</strong> ${userName}<br>
                <strong>Telegram ID:</strong> ${telegramId}
            `;

            document.getElementById('banUserModal').style.display = 'flex';
            document.getElementById('banReason').value = '';
        }

        // Close Ban User Modal
        function closeBanUserModal() {
            document.getElementById('banUserModal').style.display = 'none';
            currentBanUserId = null;
        }

        // Confirm Ban User
        async function confirmBanUser() {
            if (!currentBanUserId) return;

            const reason = document.getElementById('banReason').value.trim() || 'No reason provided';

            try {
                const response = await fetch(`/admin/api/users/${currentBanUserId}/ban`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ reason })
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification('User banned successfully!', 'success');
                    closeBanUserModal();
                    // Refresh the page to show the updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(result.error || 'Failed to ban user', 'error');
                }
            } catch (error) {
                console.error('Error banning user:', error);
                showNotification('Error banning user', 'error');
            }
        }

        // Unban User
        async function unbanUser(telegramId) {
            if (!confirm('Are you sure you want to unban this user?')) return;

            try {
                const response = await fetch(`/admin/api/users/${telegramId}/unban`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification('User unbanned successfully!', 'success');
                    // Refresh the page to show the updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(result.error || 'Failed to unban user', 'error');
                }
            } catch (error) {
                console.error('Error unbanning user:', error);
                showNotification('Error unbanning user', 'error');
            }
        }

        // Show Delete User Modal
        function deleteUser(telegramId, userName) {
            currentDeleteUserId = telegramId;

            document.getElementById('deleteUserInfo').innerHTML = `
                <strong>User:</strong> ${userName}<br>
                <strong>Telegram ID:</strong> ${telegramId}
            `;

            document.getElementById('deleteUserModal').style.display = 'flex';
            document.getElementById('deleteConfirmation').value = '';
            document.getElementById('confirmDeleteBtn').disabled = true;
        }

        // Close Delete User Modal
        function closeDeleteUserModal() {
            document.getElementById('deleteUserModal').style.display = 'none';
            currentDeleteUserId = null;
        }

        // Confirm Delete User
        async function confirmDeleteUser() {
            if (!currentDeleteUserId) return;

            try {
                const response = await fetch(`/admin/api/users/${currentDeleteUserId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    showNotification('User deleted successfully!', 'success');
                    closeDeleteUserModal();
                    // Refresh the page to remove the deleted user
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification(result.error || 'Failed to delete user', 'error');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showNotification('Error deleting user', 'error');
            }
        }

        // Enable delete button when confirmation is typed
        document.addEventListener('DOMContentLoaded', function() {
            const deleteConfirmation = document.getElementById('deleteConfirmation');
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

            if (deleteConfirmation && confirmDeleteBtn) {
                deleteConfirmation.addEventListener('input', function() {
                    confirmDeleteBtn.disabled = this.value.toUpperCase() !== 'DELETE';
                });
            }
        });

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .spin {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>

    <!-- Add User Modal -->
    <div class="modal-overlay" id="addUserModal" style="display: none;" onclick="closeAddUserModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3><i class="bi bi-person-plus"></i> Add New User</h3>
                <button class="modal-close" onclick="closeAddUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="telegramId">Telegram ID *</label>
                        <input type="number" id="telegramId" name="telegram_id" required placeholder="Enter Telegram ID">
                    </div>
                    <div class="form-group">
                        <label for="firstName">First Name *</label>
                        <input type="text" id="firstName" name="first_name" required placeholder="Enter first name">
                    </div>
                    <div class="form-group">
                        <label for="lastName">Last Name</label>
                        <input type="text" id="lastName" name="last_name" placeholder="Enter last name (optional)">
                    </div>
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" placeholder="Enter username (optional)">
                    </div>
                    <div class="form-group">
                        <label for="balance">Initial Balance</label>
                        <input type="number" id="balance" name="balance" step="0.01" min="0" placeholder="0.00">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAddUserModal()">Cancel</button>
                <button class="btn btn-primary" onclick="submitAddUser()">Add User</button>
            </div>
        </div>
    </div>

    <!-- Ban User Modal -->
    <div class="modal-overlay" id="banUserModal" style="display: none;" onclick="closeBanUserModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3><i class="bi bi-ban"></i> Ban User</h3>
                <button class="modal-close" onclick="closeBanUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to ban this user?</p>
                <div class="user-info-preview" id="banUserInfo"></div>
                <div class="form-group">
                    <label for="banReason">Ban Reason</label>
                    <textarea id="banReason" name="ban_reason" rows="3" placeholder="Enter reason for ban (optional)"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeBanUserModal()">Cancel</button>
                <button class="btn btn-danger" onclick="confirmBanUser()">Ban User</button>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal-overlay" id="deleteUserModal" style="display: none;" onclick="closeDeleteUserModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3><i class="bi bi-trash"></i> Delete User</h3>
                <button class="modal-close" onclick="closeDeleteUserModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="warning-message">
                    <i class="bi bi-exclamation-triangle"></i>
                    <p><strong>Warning:</strong> This action cannot be undone. All user data including transactions and purchases will be permanently deleted.</p>
                </div>
                <div class="user-info-preview" id="deleteUserInfo"></div>
                <p>Type <strong>DELETE</strong> to confirm:</p>
                <input type="text" id="deleteConfirmation" placeholder="Type DELETE to confirm">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeDeleteUserModal()">Cancel</button>
                <button class="btn btn-danger" id="confirmDeleteBtn" onclick="confirmDeleteUser()" disabled>Delete User</button>
            </div>
        </div>
    </div>
</body>
</html>
